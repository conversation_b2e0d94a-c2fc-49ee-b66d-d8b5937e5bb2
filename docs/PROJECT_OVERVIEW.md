# 项目概览 - 诊间听译系统

## 🎯 项目简介

诊间听译系统是一个专门为医疗机构设计的智能语音转录和病历生成桌面应用程序。通过集成先进的语音识别技术和大语言模型，系统能够将医患对话实时转录为文字，并自动生成结构化的电子病历，显著提升医疗工作效率和记录准确性。

## ✨ 核心特性

### 🎙️ 智能语音录制
- **实时录音控制**: 支持开始、暂停、继续、停止等完整录音控制
- **高质量音频**: 支持多种音频格式和采样率配置
- **录音时长显示**: 实时显示录音时长，排除暂停时间
- **音频文件管理**: 完整的录音文件存储和管理功能

### 🔊 多平台语音识别
- **百度语音识别**: 集成百度智能云语音识别服务
- **阿里云语音识别**: 支持阿里云语音识别API
- **Web Speech API**: 浏览器原生语音识别支持
- **实时转录**: 录音过程中实时显示识别结果
- **高准确率**: 针对医疗场景优化的识别准确率

### 🤖 AI病历生成
- **多模型支持**: 集成OpenAI GPT、Claude、智谱AI、通义千问等主流LLM
- **结构化输出**: 自动生成包含主诉、现病史、体格检查、诊断等完整病历
- **医疗专业性**: 针对医疗场景定制的提示词和输出格式
- **可编辑修改**: 生成的病历支持后续编辑和完善

### 📋 病历管理系统
- **病历列表**: 完整的病历记录管理和查看功能
- **搜索筛选**: 支持按患者姓名、时间等条件筛选
- **导出功能**: 支持多种格式的病历导出
- **版本控制**: 病历修改历史记录和版本管理

### ⚙️ 灵活配置系统
- **统一设置管理**: 重构后的统一设置管理系统
- **多服务商配置**: 支持配置多个语音识别和LLM服务商
- **参数调优**: 支持温度、最大Token数等AI参数调整
- **连接测试**: 内置服务连接测试功能

### 🔐 用户认证系统
- **安全登录**: 基于bcrypt的密码加密存储
- **会话管理**: JWT token会话管理
- **权限控制**: 基于角色的访问控制

## 🏗️ 技术架构

### 架构概览
```
┌─────────────────────────────────────────────────────────┐
│                   诊间听译系统                            │
├─────────────────────────────────────────────────────────┤
│  渲染进程 (React + TypeScript + Ant Design)              │
│  ├── 页面组件 (Dashboard, Recording, Medical Records)    │
│  ├── 通用组件 (Forms, Layout, Common)                   │
│  ├── 服务层 (Settings, API Services)                    │
│  └── 状态管理 (Hooks, Context)                          │
├─────────────────────────────────────────────────────────┤
│  主进程 (Electron + Node.js)                            │
│  ├── IPC处理器 (Auth, Recording, Cloud, Data)           │
│  ├── 服务层 (AuthService, CloudService, RecordingService)│
│  ├── 数据库 (SQLite + 迁移脚本)                          │
│  └── 文件系统 (音频文件存储)                              │
├─────────────────────────────────────────────────────────┤
│  外部服务集成                                            │
│  ├── 语音识别 (百度ASR, 阿里云ASR, Web Speech API)        │
│  ├── 大语言模型 (OpenAI, Claude, 智谱AI, 通义千问)        │
│  └── 其他服务 (文件存储, 日志服务)                        │
└─────────────────────────────────────────────────────────┘
```

### 核心技术栈

#### 前端技术
- **Electron**: 跨平台桌面应用框架
- **React 18**: 现代化用户界面库
- **TypeScript**: 类型安全的JavaScript超集
- **Ant Design**: 企业级UI组件库
- **Styled Components**: CSS-in-JS样式解决方案

#### 后端技术
- **Node.js**: JavaScript运行时环境
- **SQLite**: 轻量级关系型数据库
- **bcryptjs**: 密码加密库
- **crypto-js**: 加密工具库
- **Axios**: HTTP客户端库

#### 开发工具
- **Webpack**: 模块打包工具
- **Babel**: JavaScript编译器
- **ESLint**: 代码质量检查工具
- **Prettier**: 代码格式化工具
- **Vitest**: 现代化测试框架

## 📊 项目统计

### 代码统计
- **总代码行数**: ~15,000+ 行
- **TypeScript文件**: 80+ 个
- **React组件**: 30+ 个
- **API接口**: 20+ 个
- **数据库表**: 8 个

### 功能模块
- ✅ **用户认证系统**: 完成度 95%
- ✅ **录音控制模块**: 完成度 100%
- ✅ **语音识别集成**: 完成度 100%
- ✅ **AI病历生成**: 完成度 100%
- ✅ **病历管理系统**: 完成度 100%
- ✅ **设置管理系统**: 完成度 100%
- ✅ **系统集成测试**: 完成度 100%
- ✅ **文档整理**: 完成度 100%

## 🎯 应用场景

### 主要使用场景
1. **口腔科诊疗**: 牙科医生与患者的诊疗对话记录
2. **内科门诊**: 内科医生的患者问诊记录
3. **专科门诊**: 各专科医生的诊疗记录
4. **医疗培训**: 医学生和住院医师的学习记录
5. **医疗质控**: 医疗质量控制和病历规范化

### 用户群体
- **主治医师**: 提高病历书写效率
- **住院医师**: 学习标准化病历书写
- **医疗助理**: 协助医生完成病历记录
- **医院管理**: 提升医疗服务质量和效率

## 🚀 项目优势

### 技术优势
1. **跨平台支持**: 基于Electron，支持Windows、macOS、Linux
2. **离线优先**: 核心功能支持离线使用，数据本地存储
3. **模块化设计**: 高度模块化的代码结构，易于维护和扩展
4. **类型安全**: 全面使用TypeScript，减少运行时错误
5. **现代化UI**: 基于Ant Design的现代化用户界面

### 业务优势
1. **提升效率**: 自动化病历生成，节省医生时间
2. **提高准确性**: AI辅助减少人工录入错误
3. **标准化**: 统一的病历格式和结构
4. **易于使用**: 直观的用户界面，学习成本低
5. **数据安全**: 本地数据存储，保护患者隐私

## 📈 发展规划

### 短期规划 (1-3个月)
- [ ] 完善用户认证系统的自动刷新问题
- [ ] 增加更多语音识别服务商支持
- [ ] 优化AI病历生成的准确性
- [ ] 添加病历模板自定义功能
- [ ] 完善错误处理和用户反馈

### 中期规划 (3-6个月)
- [ ] 支持多用户和权限管理
- [ ] 添加云端数据同步功能
- [ ] 集成更多医疗专业词库
- [ ] 支持多语言界面
- [ ] 添加数据分析和统计功能

### 长期规划 (6-12个月)
- [ ] 开发移动端应用
- [ ] 集成医院信息系统(HIS)
- [ ] 支持多科室专业化配置
- [ ] 添加语音质量评估功能
- [ ] 开发插件系统支持第三方扩展

## 🤝 团队协作

### 开发团队
- **项目负责人**: 负责项目整体规划和技术决策
- **前端开发**: React界面开发和用户体验优化
- **后端开发**: Electron主进程和数据库设计
- **AI工程师**: 语音识别和LLM集成优化
- **测试工程师**: 质量保证和自动化测试

### 协作工具
- **版本控制**: Git + GitHub
- **项目管理**: GitHub Issues + Projects
- **文档协作**: Markdown + GitHub Wiki
- **代码审查**: GitHub Pull Request
- **持续集成**: GitHub Actions

## 📞 联系方式

### 技术支持
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/your-org/dental-transcription-client
- **文档**: https://docs.example.com

### 商务合作
- **邮箱**: <EMAIL>
- **电话**: +86 400-xxx-xxxx

## 🆕 最新更新 (2025年8月10日)

### 界面优化
- **仪表板简化**: 移除不必要的提示按钮，优化指标卡布局
- **录音管理增强**: 现代化删除确认对话框，提升操作安全性
- **小窗口优化**: 简化录音完成后的界面，保持设计一致性

### 功能增强
- **录制稳定性**: 完全重构小窗口录制逻辑，提升稳定性
- **错误处理**: 全面优化错误处理机制，提供更好的用户反馈
- **滚动修复**: 修复小窗口病历内容滚动问题

### 性能优化
- **减少刷新**: 移除不必要的定时刷新，提升应用性能
- **资源管理**: 优化音频资源管理和内存使用
- **状态同步**: 改进录制状态管理和服务同步

### 用户体验
- **操作反馈**: 集成全局消息系统，提供清晰的操作反馈
- **界面一致性**: 统一设计语言，提升整体用户体验
- **安全性**: 增强删除操作的确认机制，防止误操作

---

*本项目概览基于诊间听译 v1.0.0 版本，随着项目发展会持续更新。*
