# 诊间听译产品 - 技术架构设计文档

## 架构概览

### 整体架构图

```mermaid
graph TB
    subgraph "客户端应用 (Electron)"
        subgraph "主进程 (Main Process)"
            MP[窗口管理]
            FS[文件系统]
            DB[数据库操作]
            SEC[安全控制]
        end
        
        subgraph "渲染进程 (Renderer Process)"
            subgraph "用户界面层"
                LOGIN[登录界面]
                DASH[主控面板]
                REC[录音控制]
                MED[病历展示]
            end
            
            subgraph "业务逻辑层"
                AUTH[认证服务]
                AUDIO[音频服务]
                CLOUD[云服务]
                RECORD[病历服务]
            end
            
            subgraph "数据管理层"
                STORE[状态管理]
                CACHE[缓存管理]
                API[API调用]
            end
        end
    end
    
    subgraph "本地存储"
        SQLITE[(SQLite数据库)]
        FILES[音频文件]
        CONFIG[配置文件]
    end
    
    subgraph "云端服务"
        ASR[阿里云ASR]
        LLM[OpenAI GPT]
    end
    
    MP --> SQLITE
    MP --> FILES
    MP --> CONFIG
    
    AUDIO --> FILES
    CLOUD --> ASR
    CLOUD --> LLM
    
    AUTH --> SQLITE
    RECORD --> SQLITE
```

## 核心模块设计

### 1. 用户认证模块 (Authentication Module)

#### 接口定义
```typescript
interface IAuthService {
  login(credentials: LoginCredentials): Promise<AuthResult>
  logout(): Promise<void>
  getCurrentUser(): User | null
  isAuthenticated(): boolean
  refreshToken(): Promise<string>
}

interface LoginCredentials {
  username: string
  password: string
}

interface AuthResult {
  success: boolean
  user?: User
  token?: string
  error?: string
}

interface User {
  id: number
  username: string
  role: string
  lastLogin: Date
}
```

#### 实现要点
- 密码使用bcrypt加密存储
- JWT token用于会话管理
- 自动登录功能（记住密码）
- 会话超时处理

### 2. 录音控制模块 (Recording Module)

#### 接口定义
```typescript
interface IRecordingService {
  startRecording(config?: RecordingConfig): Promise<void>
  pauseRecording(): void
  resumeRecording(): void
  stopRecording(): Promise<AudioFile>
  getRecordingStatus(): RecordingStatus
  deleteRecording(id: string): Promise<void>
  listRecordings(): Promise<AudioFile[]>
}

interface RecordingConfig {
  sampleRate: number
  channels: number
  bitDepth: number
  format: 'wav' | 'mp3'
}

interface AudioFile {
  id: string
  filename: string
  path: string
  duration: number
  size: number
  createdAt: Date
  status: 'recording' | 'completed' | 'processing' | 'error'
}

enum RecordingStatus {
  IDLE = 'idle',
  RECORDING = 'recording',
  PAUSED = 'paused',
  PROCESSING = 'processing'
}
```

#### 技术实现
```typescript
class RecordingService implements IRecordingService {
  private mediaRecorder: MediaRecorder | null = null
  private audioStream: MediaStream | null = null
  private audioChunks: Blob[] = []
  private startTime: number = 0

  async startRecording(config: RecordingConfig = DEFAULT_CONFIG): Promise<void> {
    try {
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: config.sampleRate,
          channelCount: config.channels,
          echoCancellation: true,
          noiseSuppression: true
        }
      })

      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/webm;codecs=opus'
      })

      this.setupRecorderEvents()
      this.mediaRecorder.start(1000) // 1秒间隔收集数据
      this.startTime = Date.now()
      
    } catch (error) {
      throw new Error(`录音启动失败: ${error.message}`)
    }
  }

  private setupRecorderEvents(): void {
    if (!this.mediaRecorder) return

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data)
      }
    }

    this.mediaRecorder.onstop = async () => {
      const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' })
      const audioFile = await this.saveAudioFile(audioBlob)
      this.cleanup()
      return audioFile
    }
  }

  private async saveAudioFile(blob: Blob): Promise<AudioFile> {
    const filename = `recording_${Date.now()}.wav`
    const path = await window.electronAPI.saveAudioFile(filename, blob)
    
    const audioFile: AudioFile = {
      id: generateUUID(),
      filename,
      path,
      duration: (Date.now() - this.startTime) / 1000,
      size: blob.size,
      createdAt: new Date(),
      status: 'completed'
    }

    await this.saveToDatabase(audioFile)
    return audioFile
  }
}
```

### 3. 云服务集成模块 (Cloud Service Module)

#### 接口定义
```typescript
interface ICloudService {
  transcribeAudio(audioFile: AudioFile): Promise<TranscriptionResult>
  generateMedicalRecord(text: string, context?: MedicalContext): Promise<MedicalRecord>
  testConnection(): Promise<boolean>
}

interface TranscriptionResult {
  text: string
  confidence: number
  segments: TranscriptionSegment[]
  processingTime: number
}

interface TranscriptionSegment {
  text: string
  startTime: number
  endTime: number
  confidence: number
}

interface MedicalContext {
  patientAge?: number
  patientGender?: string
  specialty: string
  previousRecords?: string[]
}

interface MedicalRecord {
  patientInfo: PatientInfo
  chiefComplaint: string
  presentIllness: string
  examination: string
  diagnosis: string
  treatmentPlan: string
  medications: Medication[]
  followUp: string
  notes: string
}
```

#### ASR服务实现
```typescript
class AliCloudASRService {
  private readonly accessKeyId: string
  private readonly accessKeySecret: string
  private readonly endpoint: string

  async transcribeAudio(audioFile: AudioFile): Promise<TranscriptionResult> {
    const audioData = await this.readAudioFile(audioFile.path)
    
    const request = {
      audio: audioData.toString('base64'),
      format: 'wav',
      rate: 16000,
      channel: 1
    }

    const response = await this.makeAPICall('/transcribe', request)
    
    return {
      text: response.result,
      confidence: response.confidence || 0.9,
      segments: this.parseSegments(response.segments || []),
      processingTime: response.processingTime || 0
    }
  }

  private async makeAPICall(endpoint: string, data: any): Promise<any> {
    const signature = this.generateSignature(data)
    
    const response = await axios.post(`${this.endpoint}${endpoint}`, data, {
      headers: {
        'Authorization': `Bearer ${signature}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    })

    if (response.data.code !== 200) {
      throw new Error(`ASR服务错误: ${response.data.message}`)
    }

    return response.data.data
  }
}
```

#### LLM服务实现
```typescript
class OpenAILLMService {
  private readonly apiKey: string
  private readonly baseURL: string

  async generateMedicalRecord(text: string, context?: MedicalContext): Promise<MedicalRecord> {
    const prompt = this.buildMedicalPrompt(text, context)
    
    const response = await axios.post(`${this.baseURL}/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: this.getSystemPrompt()
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    }, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    })

    const content = response.data.choices[0].message.content
    return this.parseMedicalRecord(content)
  }

  private getSystemPrompt(): string {
    return `你是一个专业的口腔科医生助手，负责根据医患对话内容生成结构化的电子病历。
请按照以下JSON格式输出：
{
  "patientInfo": {
    "age": "年龄",
    "gender": "性别",
    "phone": "联系电话"
  },
  "chiefComplaint": "主诉",
  "presentIllness": "现病史",
  "examination": "检查结果",
  "diagnosis": "诊断",
  "treatmentPlan": "治疗方案",
  "medications": [
    {
      "name": "药品名称",
      "dosage": "用法用量",
      "duration": "用药时间"
    }
  ],
  "followUp": "复诊安排",
  "notes": "备注"
}`
  }

  private buildMedicalPrompt(text: string, context?: MedicalContext): string {
    let prompt = `请根据以下医患对话内容，生成结构化的口腔科电子病历：\n\n对话内容：\n${text}\n\n`
    
    if (context) {
      prompt += `患者信息：\n`
      if (context.patientAge) prompt += `年龄：${context.patientAge}岁\n`
      if (context.patientGender) prompt += `性别：${context.patientGender}\n`
      prompt += `科室：${context.specialty}\n\n`
    }
    
    prompt += `请严格按照JSON格式输出，确保所有字段都有合理的内容。`
    
    return prompt
  }
}
```

### 4. 数据持久化设计

#### 数据库连接管理
```typescript
class DatabaseManager {
  private db: Database | null = null

  async initialize(): Promise<void> {
    const dbPath = path.join(app.getPath('userData'), 'dental-transcription.db')
    this.db = new sqlite3.Database(dbPath)
    
    await this.runMigrations()
  }

  async runMigrations(): Promise<void> {
    const migrations = [
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )`,
      
      `CREATE TABLE IF NOT EXISTS recordings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        duration INTEGER,
        file_size INTEGER,
        status VARCHAR(20) DEFAULT 'completed',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`,
      
      // 其他表的创建语句...
    ]

    for (const migration of migrations) {
      await this.execute(migration)
    }
  }

  async execute(sql: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      this.db!.run(sql, params, function(err) {
        if (err) reject(err)
        else resolve({ id: this.lastID, changes: this.changes })
      })
    })
  }

  async query(sql: string, params: any[] = []): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.db!.all(sql, params, (err, rows) => {
        if (err) reject(err)
        else resolve(rows)
      })
    })
  }
}
```

## 状态管理设计

### Redux Store结构
```typescript
interface RootState {
  auth: AuthState
  recording: RecordingState
  medicalRecord: MedicalRecordState
  ui: UIState
}

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
}

interface RecordingState {
  currentRecording: AudioFile | null
  recordings: AudioFile[]
  status: RecordingStatus
  isProcessing: boolean
  error: string | null
}

interface MedicalRecordState {
  records: MedicalRecord[]
  currentRecord: MedicalRecord | null
  isGenerating: boolean
  error: string | null
}

interface UIState {
  currentPage: string
  sidebarCollapsed: boolean
  notifications: Notification[]
}
```

## 安全设计

### 数据加密
- 用户密码使用bcrypt加密
- 敏感配置信息使用AES加密存储
- 音频文件可选择本地加密存储

### 网络安全
- HTTPS通信
- API密钥安全存储
- 请求签名验证

### 数据隐私
- 本地数据存储，不上传到第三方
- 音频文件自动清理机制
- 用户数据导出功能

## 性能优化

### 音频处理优化
- 流式音频处理
- 音频压缩算法
- 异步上传机制

### 界面响应优化
- 虚拟滚动
- 懒加载
- 防抖处理

### 内存管理
- 音频数据及时释放
- 组件卸载清理
- 缓存策略优化

---

*本文档描述了系统的核心技术架构，将在开发过程中根据实际情况进行调整和完善。*
