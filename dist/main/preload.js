"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// 暴露安全的API给渲染进程
const electronAPI = {
    // 认证相关API
    auth: {
        login: (credentials) => electron_1.ipcRenderer.invoke('auth:login', credentials),
        logout: (sessionToken) => electron_1.ipcRenderer.invoke('auth:logout', sessionToken),
        getCurrentUser: (sessionToken) => electron_1.ipcRenderer.invoke('auth:getCurrentUser', sessionToken),
        changePassword: (data) => electron_1.ipcRenderer.invoke('auth:changePassword', data)
    },
    // 录音相关API
    recording: {
        start: (config) => electron_1.ipcRenderer.invoke('recording:start', config),
        stop: () => electron_1.ipcRenderer.invoke('recording:stop'),
        pause: () => electron_1.ipcRenderer.invoke('recording:pause'),
        resume: () => electron_1.ipcRenderer.invoke('recording:resume'),
        save: (data) => electron_1.ipcRenderer.invoke('recording:save', data),
        delete: (id) => electron_1.ipcRenderer.invoke('recording:delete', id),
        list: () => electron_1.ipcRenderer.invoke('recording:list'),
        rename: (id, newFilename) => electron_1.ipcRenderer.invoke('recording:rename', id, newFilename),
        getPlaybackUrl: (id) => electron_1.ipcRenderer.invoke('recording:getPlaybackUrl', id),
        updateTranscription: (id, transcription) => electron_1.ipcRenderer.invoke('recording:updateTranscription', id, transcription)
    },
    // 云服务相关API
    cloud: {
        transcribe: (recordingId) => electron_1.ipcRenderer.invoke('cloud:transcribe', recordingId),
        generateMedicalRecord: (data) => electron_1.ipcRenderer.invoke('cloud:generateMedicalRecord', data),
        updateConfig: (config) => electron_1.ipcRenderer.invoke('cloud:updateConfig', config),
        testConnection: () => electron_1.ipcRenderer.invoke('cloud:testConnection')
    },
    // 数据相关API
    data: {
        getMedicalRecords: (params) => electron_1.ipcRenderer.invoke('data:getMedicalRecords', params),
        saveMedicalRecord: (record) => electron_1.ipcRenderer.invoke('data:saveMedicalRecord', record),
        deleteRecord: (id) => electron_1.ipcRenderer.invoke('data:deleteRecord', id)
    },
    // 配置相关API
    config: {
        get: () => electron_1.ipcRenderer.invoke('config:get'),
        update: (config) => electron_1.ipcRenderer.invoke('config:update', config)
    },
    // 系统相关API
    system: {
        getVersion: () => electron_1.ipcRenderer.invoke('system:getVersion'),
        openExternal: (url) => electron_1.ipcRenderer.invoke('system:openExternal', url)
    },
    // 窗口控制API
    windowControl: (action) => {
        return electron_1.ipcRenderer.invoke('window:control', action);
    },
    // 小窗口控制API
    miniRecorder: {
        show: () => electron_1.ipcRenderer.invoke('mini-recorder:show'),
        hide: () => electron_1.ipcRenderer.invoke('mini-recorder:hide'),
        toggle: () => electron_1.ipcRenderer.invoke('mini-recorder:toggle')
    },
    // 通用IPC调用
    invoke: (channel, ...args) => electron_1.ipcRenderer.invoke(channel, ...args),
    // 事件监听
    on: (channel, callback) => {
        const validChannels = [
            'menu-new-recording',
            'menu-about',
            'recording-status-changed',
            'transcription-completed',
            'medical-record-generated'
        ];
        if (validChannels.includes(channel)) {
            electron_1.ipcRenderer.on(channel, callback);
        }
    },
    off: (channel, callback) => {
        electron_1.ipcRenderer.removeListener(channel, callback);
    }
};
// 将API暴露给渲染进程
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
// 调试信息
console.log('Preload script loaded successfully');
console.log('ElectronAPI exposed:', electronAPI);
