<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>诊间听译</title><script>// Define global variables for Electron renderer process
      if (typeof global === 'undefined') {
        var global = globalThis;
      }</script><style>body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        #root {
            height: 100vh;
            width: 100vw;
        }
        
        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e6f7ff;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }</style><script defer="defer" src="bundle.js"></script></head><body><div id="root"><div class="loading"><div class="loading-spinner"></div></div><div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1000;"><h2 style="color: #1890ff; margin: 0 0 10px 0;">测试页面</h2><p>如果您能看到这个消息，说明HTML加载正常</p><p>React应用正在初始化中...</p></div></div></body></html>