{"name": "project", "version": "1.1.0", "description": "## 项目概述", "main": "dist/main/main.js", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main:wait\"", "dev:main:wait": "sleep 3 && npm run dev:main", "dev:renderer": "webpack serve --config webpack.config.js --mode development", "dev:main": "tsc -p src/main/tsconfig.json && NODE_ENV=development electron dist/main/main.js", "build": "npm run build:renderer && npm run build:main", "build:renderer": "webpack --config webpack.config.js --mode production", "build:main": "tsc -p src/main/tsconfig.json", "start": "electron dist/main/main.js", "pack": "electron-builder --dir", "dist": "npm run build && electron-builder && npm run verify:dmg", "dist:demo": "npm run build && electron-builder --mac --arm64 && npm run verify:dmg", "dist:safe": "npm run clean && npm run build && electron-builder --mac --arm64 && npm run verify:dmg", "verify:dmg": "scripts/verify-dmg.sh", "clean": "rm -rf dist dist-build node_modules/.cache", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.8.2", "antd": "^5.26.7", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-redux": "^9.2.0", "react-router-dom": "^7.7.1", "sqlite3": "^5.1.7", "styled-components": "^6.1.19", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/react-redux": "^7.1.34", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "buffer": "^6.0.3", "concurrently": "^9.2.0", "css-loader": "^7.1.2", "electron": "^37.2.5", "electron-builder": "^26.0.12", "events": "^3.3.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.9.2", "util": "^0.12.5", "wait-on": "^8.0.4", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "build": {"appId": "com.dental.transcription.demo", "productName": "诊间听译演示版", "files": ["dist/main/**/*", "dist/renderer/**/*", "package.json", "!node_modules/**/*", "node_modules/sqlite3/**/*", "node_modules/bindings/**/*", "node_modules/file-uri-to-path/**/*", "node_modules/node-addon-api/**/*", "node_modules/prebuild-install/**/*", "node_modules/detect-libc/**/*", "node_modules/expand-template/**/*", "node_modules/github-from-package/**/*", "node_modules/minimist/**/*", "node_modules/mkdirp-classic/**/*", "node_modules/napi-build-utils/**/*", "node_modules/node-abi/**/*", "node_modules/semver/**/*", "node_modules/pump/**/*", "node_modules/end-of-stream/**/*", "node_modules/once/**/*", "node_modules/wrappy/**/*", "node_modules/rc/**/*", "node_modules/deep-extend/**/*", "node_modules/ini/**/*", "node_modules/strip-json-comments/**/*", "node_modules/simple-get/**/*", "node_modules/decompress-response/**/*", "node_modules/mimic-response/**/*", "node_modules/simple-concat/**/*", "node_modules/tar-fs/**/*", "node_modules/chownr/**/*", "node_modules/tar-stream/**/*", "node_modules/bl/**/*", "node_modules/buffer/**/*", "node_modules/base64-js/**/*", "node_modules/ieee754/**/*", "node_modules/inherits/**/*", "node_modules/readable-stream/**/*", "node_modules/string_decoder/**/*", "node_modules/safe-buffer/**/*", "node_modules/util-deprecate/**/*", "node_modules/fs-constants/**/*", "node_modules/tunnel-agent/**/*", "node_modules/tar/**/*", "node_modules/fs-minipass/**/*", "node_modules/minipass/**/*", "node_modules/yallist/**/*", "node_modules/minizlib/**/*", "node_modules/mkdirp/**/*", "node_modules/bcryptjs/**/*"], "asarUnpack": ["node_modules/sqlite3/**/*"], "nodeGypRebuild": false, "npmRebuild": false, "directories": {"output": "dist-build"}, "mac": {"target": {"target": "dmg", "arch": ["arm64", "x64"]}, "icon": "assets/icon.icns", "category": "public.app-category.medical", "hardenedRuntime": false, "gatekeeperAssess": false, "identity": null}, "dmg": {"format": "UDZO", "writeUpdateInfo": false, "internetEnabled": false, "sign": false, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "compression": "normal", "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}