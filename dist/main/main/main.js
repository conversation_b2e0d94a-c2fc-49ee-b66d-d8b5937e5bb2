"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const fs = __importStar(require("fs/promises"));
const fsSync = __importStar(require("fs"));
const connection_1 = require("./database/connection");
const authService_1 = require("./services/authService");
const cloudService_1 = require("./services/cloudService");
const medicalRecordWindowManager_1 = require("./windows/medicalRecordWindowManager");
const demoWindowManager_1 = require("./windows/demoWindowManager");
class DentalTranscriptionApp {
    constructor() {
        this.mainWindow = null;
        this.miniRecorderWindow = null;
        this.databaseManager = new connection_1.DatabaseManager();
        this.authService = new authService_1.AuthService(this.databaseManager);
        this.cloudServiceManager = new cloudService_1.CloudServiceManager();
        this.medicalRecordWindowManager = medicalRecordWindowManager_1.MedicalRecordWindowManager.getInstance();
        this.demoWindowManager = demoWindowManager_1.DemoWindowManager.getInstance();
        this.recordingsDir = path.join(electron_1.app.getPath('userData'), 'recordings');
        this.initializeApp();
    }
    initializeApp() {
        // 当Electron完成初始化时创建窗口
        electron_1.app.whenReady().then(() => {
            this.createWindow();
            this.setupIPC();
            this.initializeDatabase();
            // 预创建小窗口以提高切换速度
            setTimeout(() => {
                this.preCreateMiniWindow();
            }, 2000); // 主窗口加载完成后2秒预创建
            electron_1.app.on('activate', () => {
                if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                    this.createWindow();
                }
            });
        });
        // 当所有窗口关闭时退出应用
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        // 应用退出前清理资源
        electron_1.app.on('before-quit', async () => {
            await this.cleanup();
        });
    }
    createWindow() {
        // 创建主窗口
        const preloadPath = path.join(__dirname, 'preload.js');
        console.log('Preload path:', preloadPath);
        console.log('Preload file exists:', require('fs').existsSync(preloadPath));
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 700,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                webSecurity: false, // 开发模式下禁用web安全
                preload: preloadPath
            },
            titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
            titleBarOverlay: process.platform === 'darwin' ? {
                color: '#f9fafb',
                symbolColor: '#333333',
                height: 40
            } : undefined,
            frame: true,
            resizable: true,
            maximizable: true,
            minimizable: true,
            show: false, // 先不显示，等加载完成后再显示
            backgroundColor: '#f9fafb', // 设置背景色匹配主题
            vibrancy: process.platform === 'darwin' ? 'under-window' : undefined,
            icon: process.platform !== 'darwin' ? path.join(__dirname, '../../assets/icon.png') : undefined
        });
        // 加载应用
        const isDev = process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
        console.log('Development mode:', isDev);
        if (isDev) {
            // 等待开发服务器启动
            console.log('Loading development server...');
            // 添加错误处理
            this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
                console.error('Failed to load:', errorCode, errorDescription, validatedURL);
            });
            this.mainWindow.webContents.on('did-finish-load', () => {
                console.log('Page loaded successfully');
            });
            setTimeout(() => {
                console.log('Attempting to load http://localhost:3000');
                this.mainWindow?.loadURL('http://localhost:3000').catch(err => {
                    console.error('Error loading URL:', err);
                });
                this.mainWindow?.webContents.openDevTools();
            }, 3000);
        }
        else {
            // 在打包版本中，文件在 ASAR 中
            const htmlPath = path.join(__dirname, '../renderer/index.html');
            console.log('Loading HTML from:', htmlPath);
            console.log('__dirname:', __dirname);
            console.log('app.isPackaged:', electron_1.app.isPackaged);
            this.mainWindow.loadFile(htmlPath).catch(err => {
                console.error('Error loading HTML file:', err);
            });
        }
        // 窗口准备好后显示
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow?.show();
        });
        // 窗口关闭时清理引用
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        // 设置菜单
        this.setupMenu();
    }
    preCreateMiniWindow() {
        // 预创建小窗口但不显示，提高切换速度
        if (this.miniRecorderWindow) {
            return; // 已经存在，无需重复创建
        }
        const preloadPath = path.join(__dirname, 'preload.js');
        const { screen } = require('electron');
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
        const windowWidth = 420;
        const windowHeight = 80;
        const margin = 20;
        this.miniRecorderWindow = new electron_1.BrowserWindow({
            width: windowWidth,
            height: windowHeight,
            minWidth: 380,
            minHeight: 70,
            maxWidth: 500,
            maxHeight: 90,
            x: screenWidth - windowWidth - margin,
            y: screenHeight - windowHeight - margin,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                webSecurity: false,
                preload: preloadPath
            },
            frame: false,
            titleBarStyle: 'customButtonsOnHover',
            trafficLightPosition: { x: -100, y: -100 },
            resizable: true,
            minimizable: false,
            maximizable: false,
            closable: true,
            alwaysOnTop: true,
            skipTaskbar: true,
            show: false, // 预创建时不显示
            backgroundColor: '#ffffff',
            transparent: false,
            hasShadow: true,
            roundedCorners: true,
            icon: process.platform !== 'darwin' ? path.join(__dirname, '../../assets/icon.png') : undefined
        });
        // 加载应用
        const isDev = process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
        if (isDev) {
            this.miniRecorderWindow.loadURL('http://localhost:3000?mode=mini').catch(err => {
                console.error('Error loading mini window URL:', err);
            });
        }
        else {
            const htmlPath = path.join(__dirname, '../renderer/index.html');
            this.miniRecorderWindow.loadFile(htmlPath, {
                query: { mode: 'mini' }
            }).catch(err => {
                console.error('Error loading mini window HTML file:', err);
            });
        }
        // 窗口关闭时清理引用并显示主窗口
        this.miniRecorderWindow.on('closed', () => {
            this.miniRecorderWindow = null;
            if (this.mainWindow) {
                this.mainWindow.show();
                this.mainWindow.focus();
            }
        });
        console.log('小窗口预创建完成');
    }
    createMiniRecorderWindow() {
        // 如果已经存在小窗口，则显示它
        if (this.miniRecorderWindow) {
            this.miniRecorderWindow.show();
            this.miniRecorderWindow.focus();
            // 隐藏主窗口
            if (this.mainWindow) {
                this.mainWindow.hide();
            }
            return;
        }
        // 如果窗口未预创建，则立即创建
        this.preCreateMiniWindow();
    }
    setupMenu() {
        const template = [
            {
                label: '文件',
                submenu: [
                    {
                        label: '新建录音',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-new-recording');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '退出',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            electron_1.app.quit();
                        }
                    }
                ]
            },
            {
                label: '编辑',
                submenu: [
                    { role: 'undo', label: '撤销' },
                    { role: 'redo', label: '重做' },
                    { type: 'separator' },
                    { role: 'cut', label: '剪切' },
                    { role: 'copy', label: '复制' },
                    { role: 'paste', label: '粘贴' }
                ]
            },
            {
                label: '视图',
                submenu: [
                    { role: 'reload', label: '重新加载' },
                    { role: 'forceReload', label: '强制重新加载' },
                    { role: 'toggleDevTools', label: '开发者工具' },
                    { type: 'separator' },
                    { role: 'resetZoom', label: '实际大小' },
                    { role: 'zoomIn', label: '放大' },
                    { role: 'zoomOut', label: '缩小' },
                    { type: 'separator' },
                    { role: 'togglefullscreen', label: '全屏' }
                ]
            },
            {
                label: '帮助',
                submenu: [
                    {
                        label: '关于',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-about');
                        }
                    }
                ]
            }
        ];
        const menu = electron_1.Menu.buildFromTemplate(template);
        electron_1.Menu.setApplicationMenu(menu);
    }
    setupIPC() {
        // 认证相关IPC
        electron_1.ipcMain.handle('auth:login', async (event, credentials) => {
            try {
                const session = await this.authService.login(credentials);
                if (session) {
                    return {
                        success: true,
                        user: session.user,
                        sessionToken: session.sessionToken,
                        expiresAt: session.expiresAt
                    };
                }
                else {
                    return { success: false, error: '用户名或密码错误' };
                }
            }
            catch (error) {
                console.error('登录失败:', error);
                return { success: false, error: '登录失败' };
            }
        });
        electron_1.ipcMain.handle('auth:logout', async (event, sessionToken) => {
            try {
                const success = await this.authService.logout(sessionToken);
                return { success };
            }
            catch (error) {
                console.error('登出失败:', error);
                return { success: false, error: '登出失败' };
            }
        });
        electron_1.ipcMain.handle('auth:getCurrentUser', async (event, sessionToken) => {
            try {
                const user = await this.authService.getCurrentUser(sessionToken);
                if (user) {
                    return { success: true, user };
                }
                else {
                    return { success: false, error: '会话已过期' };
                }
            }
            catch (error) {
                console.error('获取用户信息失败:', error);
                return { success: false, error: '获取用户信息失败' };
            }
        });
        electron_1.ipcMain.handle('auth:changePassword', async (event, data) => {
            try {
                const { userId, oldPassword, newPassword } = data;
                const success = await this.authService.changePassword(userId, oldPassword, newPassword);
                if (success) {
                    return { success: true };
                }
                else {
                    return { success: false, error: '旧密码错误' };
                }
            }
            catch (error) {
                console.error('修改密码失败:', error);
                return { success: false, error: '修改密码失败' };
            }
        });
        // 刷新会话token
        electron_1.ipcMain.handle('auth:refreshSession', async (event, sessionToken) => {
            try {
                const session = await this.authService.refreshSession(sessionToken);
                if (session) {
                    return {
                        success: true,
                        sessionToken: session.sessionToken,
                        user: session.user,
                        expiresAt: session.expiresAt
                    };
                }
                else {
                    return { success: false, error: '会话刷新失败' };
                }
            }
            catch (error) {
                console.error('刷新会话失败:', error);
                return { success: false, error: '刷新会话失败' };
            }
        });
        // 延长会话
        electron_1.ipcMain.handle('auth:extendSession', async (event, sessionToken) => {
            try {
                const success = await this.authService.extendSession(sessionToken);
                return { success };
            }
            catch (error) {
                console.error('延长会话失败:', error);
                return { success: false, error: '延长会话失败' };
            }
        });
        // 获取会话信息
        electron_1.ipcMain.handle('auth:getSessionInfo', async (event, sessionToken) => {
            try {
                const info = await this.authService.getSessionInfo(sessionToken);
                return { success: true, data: info };
            }
            catch (error) {
                console.error('获取会话信息失败:', error);
                return { success: false, error: '获取会话信息失败' };
            }
        });
        // 录音相关IPC
        electron_1.ipcMain.handle('recording:start', async (event, config) => {
            // TODO: 实现录音开始逻辑
            return { success: true, recordingId: 'temp-id' };
        });
        electron_1.ipcMain.handle('recording:stop', async () => {
            // TODO: 实现录音停止逻辑
            return { success: true, audioFile: { id: 'temp-id', filename: 'recording.wav' } };
        });
        // 保存录音文件
        electron_1.ipcMain.handle('recording:save', async (event, data) => {
            try {
                await this.ensureRecordingsDir();
                const { id, filename, audioData, transcription, metadata } = data;
                const filePath = path.join(this.recordingsDir, filename);
                // 保存音频文件
                const buffer = Buffer.from(audioData);
                await fs.writeFile(filePath, buffer);
                // 保存到数据库，获取生成的ID
                const recordingId = await this.databaseManager.saveRecording({
                    id,
                    filename,
                    path: filePath,
                    transcription,
                    ...metadata
                });
                return { success: true, path: filePath, id: recordingId };
            }
            catch (error) {
                console.error('保存录音失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 获取录音列表
        electron_1.ipcMain.handle('recording:list', async () => {
            try {
                const recordings = await this.databaseManager.getRecordings();
                return { success: true, recordings };
            }
            catch (error) {
                console.error('获取录音列表失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 删除录音
        electron_1.ipcMain.handle('recording:delete', async (event, id) => {
            try {
                const recording = await this.databaseManager.getRecording(id);
                if (recording) {
                    // 删除文件
                    try {
                        await fs.unlink(recording.path);
                    }
                    catch (err) {
                        console.warn('删除录音文件失败:', err);
                    }
                    // 从数据库删除
                    await this.databaseManager.deleteRecording(id);
                }
                return { success: true, id };
            }
            catch (error) {
                console.error('删除录音失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 重命名录音
        electron_1.ipcMain.handle('recording:rename', async (event, id, newFilename) => {
            try {
                const recording = await this.databaseManager.getRecording(id);
                if (!recording) {
                    throw new Error('录音不存在');
                }
                const oldPath = recording.path;
                const newPath = path.join(path.dirname(oldPath), newFilename);
                // 重命名文件
                await fs.rename(oldPath, newPath);
                // 更新数据库
                await this.databaseManager.updateRecording(id, {
                    filename: newFilename,
                    path: newPath
                });
                return { success: true };
            }
            catch (error) {
                console.error('重命名录音失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 获取播放URL
        electron_1.ipcMain.handle('recording:getPlaybackUrl', async (event, id) => {
            try {
                const recording = await this.databaseManager.getRecording(id);
                if (!recording || !fsSync.existsSync(recording.path)) {
                    throw new Error('录音文件不存在');
                }
                // 返回文件路径作为URL
                return { success: true, url: `file://${recording.path}` };
            }
            catch (error) {
                console.error('获取播放URL失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 更新转录
        electron_1.ipcMain.handle('recording:updateTranscription', async (event, id, transcription) => {
            try {
                await this.databaseManager.updateRecording(id, { transcription });
                return { success: true };
            }
            catch (error) {
                console.error('更新转录失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 病历相关IPC处理程序
        // 保存病历记录
        electron_1.ipcMain.handle('medical-record:save', async (event, data) => {
            try {
                await this.databaseManager.saveMedicalRecord(data);
                return { success: true };
            }
            catch (error) {
                console.error('保存病历失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 获取病历记录
        electron_1.ipcMain.handle('medical-record:get', async (event, transcriptionId) => {
            try {
                const data = await this.databaseManager.getMedicalRecord(transcriptionId);
                return { success: true, data };
            }
            catch (error) {
                console.error('获取病历失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 获取所有病历记录
        electron_1.ipcMain.handle('medical-record:getAll', async (event) => {
            try {
                const data = await this.databaseManager.getAllMedicalRecords();
                return { success: true, data };
            }
            catch (error) {
                console.error('获取病历列表失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 更新病历记录
        electron_1.ipcMain.handle('medical-record:update', async (event, id, updates) => {
            try {
                await this.databaseManager.updateMedicalRecord(id, updates);
                return { success: true };
            }
            catch (error) {
                console.error('更新病历失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 删除病历记录
        electron_1.ipcMain.handle('medical-record:delete', async (event, id) => {
            try {
                await this.databaseManager.deleteMedicalRecord(id);
                return { success: true };
            }
            catch (error) {
                console.error('删除病历失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 数据库相关IPC
        electron_1.ipcMain.handle('db:query', async (event, sql, params) => {
            try {
                const result = await this.databaseManager.query(sql, params);
                return { success: true, data: result };
            }
            catch (error) {
                return { success: false, error: error.message };
            }
        });
        electron_1.ipcMain.handle('db:execute', async (event, sql, params) => {
            try {
                const result = await this.databaseManager.execute(sql, params);
                return { success: true, data: result };
            }
            catch (error) {
                return { success: false, error: error.message };
            }
        });
        // 云服务相关IPC处理器
        // 音频转录
        electron_1.ipcMain.handle('cloud:transcribe', async (event, recordingId) => {
            try {
                // 获取录音文件信息
                const recording = await this.databaseManager.getRecording(recordingId);
                if (!recording) {
                    throw new Error('录音记录不存在');
                }
                if (!fsSync.existsSync(recording.path)) {
                    throw new Error('录音文件不存在');
                }
                // 调用云服务进行转录
                const result = await this.cloudServiceManager.transcribeAudio(recording.path);
                // 更新录音记录的转录内容
                await this.databaseManager.updateRecording(recordingId, {
                    transcription: result.text
                });
                // 发送转录完成事件
                event.sender.send('transcription-completed', {
                    recordingId,
                    transcription: result.text,
                    confidence: result.confidence
                });
                return {
                    success: true,
                    transcription: result
                };
            }
            catch (error) {
                console.error('音频转录失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 生成病历
        electron_1.ipcMain.handle('cloud:generateMedicalRecord', async (event, data) => {
            try {
                const { transcriptionText, context } = data;
                if (!transcriptionText || transcriptionText.trim().length === 0) {
                    throw new Error('转录内容不能为空');
                }
                // 调用云服务生成病历
                const result = await this.cloudServiceManager.generateMedicalRecord(transcriptionText, context);
                // 发送病历生成完成事件
                event.sender.send('medical-record-generated', {
                    medicalRecord: result
                });
                return {
                    success: true,
                    medicalRecord: result
                };
            }
            catch (error) {
                console.error('生成病历失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 更新云服务配置
        electron_1.ipcMain.handle('cloud:updateConfig', async (event, config) => {
            try {
                this.cloudServiceManager.updateConfig(config);
                return { success: true };
            }
            catch (error) {
                console.error('更新云服务配置失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 测试云服务连接
        electron_1.ipcMain.handle('cloud:testConnection', async (event) => {
            try {
                const result = await this.cloudServiceManager.testConnection();
                return { success: true, data: result };
            }
            catch (error) {
                console.error('测试云服务连接失败:', error);
                return { success: false, error: error.message };
            }
        });
        // 小窗口控制相关IPC
        electron_1.ipcMain.handle('mini-recorder:show', async () => {
            try {
                this.createMiniRecorderWindow();
                return { success: true };
            }
            catch (error) {
                console.error('显示小窗口失败:', error);
                return { success: false, error: error.message };
            }
        });
        electron_1.ipcMain.handle('mini-recorder:hide', async () => {
            try {
                if (this.miniRecorderWindow) {
                    this.miniRecorderWindow.close();
                }
                return { success: true };
            }
            catch (error) {
                console.error('隐藏小窗口失败:', error);
                return { success: false, error: error.message };
            }
        });
        electron_1.ipcMain.handle('mini-recorder:toggle', async () => {
            try {
                if (this.miniRecorderWindow && !this.miniRecorderWindow.isDestroyed()) {
                    this.miniRecorderWindow.close();
                }
                else {
                    this.createMiniRecorderWindow();
                }
                return { success: true };
            }
            catch (error) {
                console.error('切换小窗口失败:', error);
                return { success: false, error: error.message };
            }
        });
    }
    async initializeDatabase() {
        try {
            await this.databaseManager.initialize();
            console.log('数据库初始化成功');
        }
        catch (error) {
            console.error('数据库初始化失败:', error);
        }
    }
    async cleanup() {
        try {
            await this.databaseManager.close();
            console.log('应用清理完成');
        }
        catch (error) {
            console.error('应用清理失败:', error);
        }
    }
    async ensureRecordingsDir() {
        try {
            await fs.access(this.recordingsDir);
        }
        catch {
            await fs.mkdir(this.recordingsDir, { recursive: true });
            console.log('创建录音目录:', this.recordingsDir);
        }
    }
}
// 创建应用实例
new DentalTranscriptionApp();
