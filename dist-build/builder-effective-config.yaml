directories:
  output: dist-build
  buildResources: build
appId: com.dental.transcription.demo
productName: 诊间听译演示版
files:
  - filter:
      - dist/main/**/*
      - dist/renderer/**/*
      - package.json
      - '!node_modules/**/*'
      - node_modules/sqlite3/**/*
      - node_modules/bindings/**/*
      - node_modules/file-uri-to-path/**/*
      - node_modules/node-addon-api/**/*
      - node_modules/prebuild-install/**/*
      - node_modules/detect-libc/**/*
      - node_modules/expand-template/**/*
      - node_modules/github-from-package/**/*
      - node_modules/minimist/**/*
      - node_modules/mkdirp-classic/**/*
      - node_modules/napi-build-utils/**/*
      - node_modules/node-abi/**/*
      - node_modules/semver/**/*
      - node_modules/pump/**/*
      - node_modules/end-of-stream/**/*
      - node_modules/once/**/*
      - node_modules/wrappy/**/*
      - node_modules/rc/**/*
      - node_modules/deep-extend/**/*
      - node_modules/ini/**/*
      - node_modules/strip-json-comments/**/*
      - node_modules/simple-get/**/*
      - node_modules/decompress-response/**/*
      - node_modules/mimic-response/**/*
      - node_modules/simple-concat/**/*
      - node_modules/tar-fs/**/*
      - node_modules/chownr/**/*
      - node_modules/tar-stream/**/*
      - node_modules/bl/**/*
      - node_modules/buffer/**/*
      - node_modules/base64-js/**/*
      - node_modules/ieee754/**/*
      - node_modules/inherits/**/*
      - node_modules/readable-stream/**/*
      - node_modules/string_decoder/**/*
      - node_modules/safe-buffer/**/*
      - node_modules/util-deprecate/**/*
      - node_modules/fs-constants/**/*
      - node_modules/tunnel-agent/**/*
      - node_modules/tar/**/*
      - node_modules/fs-minipass/**/*
      - node_modules/minipass/**/*
      - node_modules/yallist/**/*
      - node_modules/minizlib/**/*
      - node_modules/mkdirp/**/*
      - node_modules/bcryptjs/**/*
asarUnpack:
  - node_modules/sqlite3/**/*
nodeGypRebuild: false
npmRebuild: false
mac:
  target:
    target: dmg
    arch:
      - arm64
      - x64
  icon: assets/icon.icns
  category: public.app-category.medical
  hardenedRuntime: false
  gatekeeperAssess: false
  identity: null
dmg:
  format: UDZO
  writeUpdateInfo: false
  internetEnabled: false
  sign: false
  contents:
    - x: 130
      'y': 220
    - x: 410
      'y': 220
      type: link
      path: /Applications
win:
  target: nsis
  icon: assets/icon.ico
linux:
  target: AppImage
  icon: assets/icon.png
compression: normal
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
electronVersion: 37.2.5
