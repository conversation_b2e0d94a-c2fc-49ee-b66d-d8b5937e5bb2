# 应用大小优化说明

## 问题分析

### 原始问题
- **应用包大小**: 866MB
- **DMG文件大小**: 约400MB
- **主要问题**: `app.asar` 文件达到608MB

### 问题根源
1. **整个 `node_modules` 被打包**: 包含了所有开发依赖和生产依赖
2. **递归包含 `dist` 目录**: 之前构建的应用包被重复包含
3. **未优化的文件包含策略**: 没有明确排除不必要的文件

## 依赖分析工具

为了准确识别所有必要的依赖，创建了自动化分析脚本：

```javascript
// scripts/get-dependencies.js - 分析模块依赖关系
const fs = require('fs');
const path = require('path');

function getAllDependencies(moduleName, visited = new Set()) {
  if (visited.has(moduleName)) return [];
  visited.add(moduleName);

  const packageJsonPath = path.join('node_modules', moduleName, 'package.json');
  if (!fs.existsSync(packageJsonPath)) return [];

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const dependencies = packageJson.dependencies || {};

  let allDeps = [moduleName];
  for (const dep of Object.keys(dependencies)) {
    allDeps = allDeps.concat(getAllDependencies(dep, visited));
  }
  return allDeps;
}

// 分析 sqlite3 和 bcryptjs 的完整依赖链
const sqlite3Deps = getAllDependencies('sqlite3');
const bcryptjsDeps = getAllDependencies('bcryptjs');
const allDeps = [...new Set([...sqlite3Deps, ...bcryptjsDeps])];

allDeps.forEach(dep => console.log(`"node_modules/${dep}/**/*",`));
```

使用方法：
```bash
node scripts/get-dependencies.js
```

## 优化方案

### 1. 精确控制文件包含
```json
{
  "build": {
    "files": [
      "dist/main/**/*",      // 只包含主进程代码
      "dist/renderer/**/*",  // 只包含渲染进程代码
      "package.json",
      "!node_modules/**/*",  // 排除所有 node_modules
      // 只包含 SQLite3 和 bcryptjs 及其完整依赖链（46个模块）
      "node_modules/sqlite3/**/*",
      "node_modules/bcryptjs/**/*",
      "node_modules/bindings/**/*",
      "node_modules/file-uri-to-path/**/*",
      "node_modules/node-addon-api/**/*",
      "node_modules/prebuild-install/**/*",
      "node_modules/detect-libc/**/*",
      "node_modules/expand-template/**/*",
      "node_modules/github-from-package/**/*",
      "node_modules/minimist/**/*",
      "node_modules/mkdirp-classic/**/*",
      "node_modules/napi-build-utils/**/*",
      "node_modules/node-abi/**/*",
      "node_modules/semver/**/*",
      "node_modules/pump/**/*",
      "node_modules/end-of-stream/**/*",
      "node_modules/once/**/*",
      "node_modules/wrappy/**/*",
      "node_modules/rc/**/*",
      "node_modules/deep-extend/**/*",
      "node_modules/ini/**/*",
      "node_modules/strip-json-comments/**/*",
      "node_modules/simple-get/**/*",
      "node_modules/decompress-response/**/*",
      "node_modules/mimic-response/**/*",
      "node_modules/simple-concat/**/*",
      "node_modules/tar-fs/**/*",
      "node_modules/chownr/**/*",
      "node_modules/tar-stream/**/*",
      "node_modules/bl/**/*",
      "node_modules/buffer/**/*",
      "node_modules/base64-js/**/*",
      "node_modules/ieee754/**/*",
      "node_modules/inherits/**/*",
      "node_modules/readable-stream/**/*",
      "node_modules/string_decoder/**/*",
      "node_modules/safe-buffer/**/*",
      "node_modules/util-deprecate/**/*",
      "node_modules/fs-constants/**/*",
      "node_modules/tunnel-agent/**/*",
      "node_modules/tar/**/*",
      "node_modules/fs-minipass/**/*",
      "node_modules/minipass/**/*",
      "node_modules/yallist/**/*",
      "node_modules/minizlib/**/*",
      "node_modules/mkdirp/**/*"
    ],
    "asarUnpack": [
      "node_modules/sqlite3/**/*"  // SQLite3 需要解包
    ],
    "nodeGypRebuild": false,  // 避免重新构建原生模块
    "npmRebuild": false       // 避免重新安装依赖
  }
}
```

### 2. 依赖分析
**主进程实际需要的依赖**:
- `sqlite3`: 数据库操作（原生模块）
- `bcryptjs`: 密码加密
- Node.js 内置模块: `fs`, `path`, `crypto`
- Electron 模块: `electron`

**不需要打包的依赖**:
- React 相关库（已打包到 bundle.js）
- Ant Design（已打包到 bundle.js）
- 开发工具和构建工具
- TypeScript 类型定义
- Webpack 和相关插件
- ESLint、Prettier 等代码质量工具

**关键发现**:
- 原生模块 `sqlite3` 有复杂的依赖链，总共需要46个依赖模块
- 必须使用 `!node_modules/**/*` 先排除所有依赖，再明确包含需要的
- `asarUnpack` 对原生模块很重要，确保二进制文件能正确加载
- `nodeGypRebuild: false` 和 `npmRebuild: false` 避免构建时的问题
- 创建了自动化脚本 `scripts/get-dependencies.js` 来分析依赖关系

### 3. 构建配置优化
```json
{
  "directories": {
    "output": "dist-build"  // 避免与源码 dist 目录冲突
  },
  "compression": "maximum", // 最大压缩
  "asarUnpack": [
    "node_modules/sqlite3/**/*"  // SQLite3 需要解包
  ]
}
```

## 优化结果

### 大小对比
| 项目 | 优化前 | 优化后 | 减少 |
|------|--------|--------|------|
| 应用包 (.app) | 866MB | 264MB | 70% |
| app.asar | 608MB | 7.1MB | 99% |
| DMG 文件 | ~400MB | 112MB | 72% |

### 性能影响
- ✅ **启动速度**: 更快（文件更少）
- ✅ **下载时间**: 大幅减少
- ✅ **磁盘占用**: 显著降低
- ✅ **功能完整性**: 保持不变

## 最佳实践

### 1. 依赖管理
- 明确区分生产依赖和开发依赖
- 只打包主进程实际使用的模块
- 前端依赖通过 webpack 打包，不需要运行时包含

### 2. 文件包含策略
```json
{
  "files": [
    "dist/main/**/*",        // 主进程编译后代码
    "dist/renderer/**/*",    // 前端编译后代码
    "package.json",          // 应用元数据
    "!node_modules/**/*",    // 排除所有依赖
    "node_modules/sqlite3/**/*"  // 只包含必要的原生模块
  ]
}
```

### 3. 构建流程
1. **清理旧构建**: 删除之前的构建产物
2. **编译代码**: 分别编译主进程和渲染进程
3. **精确打包**: 只包含运行时必需的文件
4. **压缩优化**: 使用最大压缩级别

### 4. 监控和维护
- 定期检查应用包大小
- 审查新增依赖的必要性
- 使用工具分析包内容

## 进一步优化建议

### 1. 代码分割
- 使用 webpack 代码分割减少初始包大小
- 按需加载非核心功能模块

### 2. 资源优化
- 压缩图片和图标文件
- 移除未使用的字体和样式

### 3. 原生模块优化
- 考虑使用更轻量的 SQLite 替代方案
- 评估是否可以用 Web Crypto API 替代 bcryptjs

### 4. 构建优化
- 启用 tree shaking 移除未使用代码
- 使用生产模式构建以获得最佳优化

## 总结

通过精确控制文件包含和依赖管理，我们成功将应用大小从866MB减少到263MB，减少了70%。这不仅改善了用户体验，还降低了分发成本。

关键是理解 Electron 应用的架构：
- **主进程**: 只需要实际使用的 Node.js 模块
- **渲染进程**: 前端代码已通过 webpack 打包
- **原生模块**: 需要特殊处理（如 sqlite3）

这种优化方法可以应用到其他 Electron 项目中，实现显著的大小减少。
