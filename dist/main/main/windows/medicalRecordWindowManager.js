"use strict";
/**
 * 病历窗口管理器
 * v1.0.1 小窗口病历生成功能
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicalRecordWindowManager = void 0;
const electron_1 = require("electron");
const path_1 = __importDefault(require("path"));
class MedicalRecordWindowManager {
    constructor() {
        this.windows = new Map();
        this.windowStates = new Map();
        this.isDev = process.env.NODE_ENV === 'development';
        this.setupIpcHandlers();
    }
    static getInstance() {
        if (!MedicalRecordWindowManager.instance) {
            MedicalRecordWindowManager.instance = new MedicalRecordWindowManager();
        }
        return MedicalRecordWindowManager.instance;
    }
    /**
     * 创建病历窗口
     */
    async createMedicalRecordWindow(parentWindow, recordData) {
        try {
            const windowId = `medical-record-${Date.now()}`;
            const parentBounds = parentWindow.getBounds();
            // 计算窗口位置和大小
            const { position, size } = this.calculateWindowBounds(parentBounds);
            // 创建窗口
            const window = new electron_1.BrowserWindow({
                width: size.width,
                height: size.height,
                x: position.x,
                y: position.y,
                // 窗口属性
                resizable: true,
                minimizable: true,
                maximizable: false,
                closable: true,
                alwaysOnTop: false,
                // 外观设置
                frame: true,
                titleBarStyle: 'default',
                backgroundColor: '#ffffff',
                // 父窗口关系
                parent: parentWindow,
                modal: false,
                // 显示设置
                show: false, // 先不显示，等内容加载完成后再显示
                // 预加载脚本
                webPreferences: {
                    preload: path_1.default.join(__dirname, '../preload.js'),
                    sandbox: false,
                    nodeIntegration: false,
                    contextIsolation: true,
                    webSecurity: true,
                    allowRunningInsecureContent: false
                },
                // 图标和标题
                title: '病历编辑器',
                icon: path_1.default.join(__dirname, '../../assets/icon.png')
            });
            // 加载页面
            console.log('开始加载病历窗口页面...');
            if (this.isDev) {
                console.log('开发模式：加载URL http://localhost:3000/medical-record');
                await window.loadURL('http://localhost:3000/medical-record');
            }
            else {
                console.log('生产模式：加载本地文件');
                await window.loadFile(path_1.default.join(__dirname, '../renderer/medical-record.html'));
            }
            console.log('病历窗口页面加载完成');
            // 设置窗口状态
            const windowState = {
                id: windowId,
                parentWindowId: parentWindow.id.toString(),
                position,
                size,
                state: 'loading',
                recordId: recordData.id,
                isVisible: false,
                isMinimized: false,
                isAlwaysOnTop: false
            };
            this.windows.set(windowId, window);
            this.windowStates.set(windowId, windowState);
            // 设置窗口事件监听
            this.setupWindowEventListeners(windowId, window);
            // 发送初始数据到窗口
            window.webContents.once('did-finish-load', () => {
                console.log('病历窗口页面加载完成，发送初始数据...');
                window.webContents.send('medical-record:init', {
                    windowId,
                    recordData,
                    config: this.getDefaultEditorConfig()
                });
                // 显示窗口
                console.log('显示病历窗口...');
                window.show();
                this.updateWindowState(windowId, { isVisible: true, state: 'editing' });
                console.log('病历窗口已显示');
            });
            console.log(`病历窗口创建成功: ${windowId}`);
            return windowId;
        }
        catch (error) {
            console.error('创建病历窗口失败:', error);
            throw error;
        }
    }
    /**
     * 计算窗口位置和大小
     */
    calculateWindowBounds(parentBounds) {
        const windowHeight = 500;
        const windowWidth = parentBounds.width;
        const gap = 10;
        // 计算位置：在父窗口上方
        let x = parentBounds.x;
        let y = parentBounds.y - windowHeight - gap;
        // 确保窗口不会超出屏幕边界
        const { screen } = require('electron');
        const display = screen.getDisplayNearestPoint({ x: parentBounds.x, y: parentBounds.y });
        const workArea = display.workArea;
        // 调整X坐标
        if (x + windowWidth > workArea.x + workArea.width) {
            x = workArea.x + workArea.width - windowWidth;
        }
        if (x < workArea.x) {
            x = workArea.x;
        }
        // 调整Y坐标
        if (y < workArea.y) {
            // 如果上方空间不够，放在父窗口下方
            y = parentBounds.y + parentBounds.height + gap;
            // 如果下方也不够，调整高度
            if (y + windowHeight > workArea.y + workArea.height) {
                const availableHeight = workArea.y + workArea.height - y - gap;
                if (availableHeight > 300) { // 最小高度300px
                    return {
                        position: { x, y },
                        size: { width: windowWidth, height: availableHeight }
                    };
                }
                else {
                    // 如果上下都不够，放在屏幕中央
                    x = workArea.x + (workArea.width - windowWidth) / 2;
                    y = workArea.y + (workArea.height - windowHeight) / 2;
                }
            }
        }
        return {
            position: { x, y },
            size: { width: windowWidth, height: windowHeight }
        };
    }
    /**
     * 设置窗口事件监听
     */
    setupWindowEventListeners(windowId, window) {
        // 窗口关闭事件
        window.on('closed', () => {
            this.handleWindowClosed(windowId);
        });
        // 窗口移动事件
        window.on('moved', () => {
            const bounds = window.getBounds();
            this.updateWindowState(windowId, {
                position: { x: bounds.x, y: bounds.y }
            });
        });
        // 窗口大小改变事件
        window.on('resized', () => {
            const bounds = window.getBounds();
            this.updateWindowState(windowId, {
                size: { width: bounds.width, height: bounds.height }
            });
        });
        // 窗口最小化事件
        window.on('minimize', () => {
            this.updateWindowState(windowId, { isMinimized: true });
        });
        // 窗口恢复事件
        window.on('restore', () => {
            this.updateWindowState(windowId, { isMinimized: false });
        });
        // 窗口显示/隐藏事件
        window.on('show', () => {
            this.updateWindowState(windowId, { isVisible: true });
        });
        window.on('hide', () => {
            this.updateWindowState(windowId, { isVisible: false });
        });
        // 窗口焦点事件
        window.on('focus', () => {
            window.webContents.send('medical-record:focus');
        });
        window.on('blur', () => {
            window.webContents.send('medical-record:blur');
        });
    }
    /**
     * 处理窗口关闭
     */
    handleWindowClosed(windowId) {
        this.windows.delete(windowId);
        this.windowStates.delete(windowId);
        console.log(`病历窗口已关闭: ${windowId}`);
    }
    /**
     * 更新窗口状态
     */
    updateWindowState(windowId, updates) {
        const currentState = this.windowStates.get(windowId);
        if (currentState) {
            const newState = { ...currentState, ...updates };
            this.windowStates.set(windowId, newState);
            // 通知窗口状态变化
            const window = this.windows.get(windowId);
            if (window && !window.isDestroyed()) {
                window.webContents.send('medical-record:state-changed', newState);
            }
        }
    }
    /**
     * 获取默认编辑器配置
     */
    getDefaultEditorConfig() {
        return {
            readOnly: false,
            autoSave: true,
            autoSaveInterval: 30000, // 30秒
            spellCheck: true,
            wordWrap: true,
            showLineNumbers: false,
            theme: 'light',
            fontSize: 14,
            fontFamily: 'Microsoft YaHei, Arial, sans-serif'
        };
    }
    /**
     * 设置IPC处理器
     */
    setupIpcHandlers() {
        // 创建病历窗口
        electron_1.ipcMain.handle('medical-record-window:create', async (event, data) => {
            try {
                console.log('收到创建病历窗口请求:', data);
                const parentWindow = electron_1.BrowserWindow.fromWebContents(event.sender);
                if (!parentWindow) {
                    throw new Error('无法获取父窗口');
                }
                console.log('父窗口获取成功，开始创建病历窗口...');
                const windowId = await this.createMedicalRecordWindow(parentWindow, data.recordData);
                console.log('病历窗口创建完成，窗口ID:', windowId);
                return { success: true, windowId };
            }
            catch (error) {
                console.error('创建病历窗口失败:', error);
                return { success: false, error: error instanceof Error ? error.message : '创建窗口失败' };
            }
        });
        // 关闭病历窗口
        electron_1.ipcMain.handle('medical-record-window:close', async (event, windowId) => {
            try {
                const window = this.windows.get(windowId);
                if (window && !window.isDestroyed()) {
                    window.close();
                }
                return { success: true };
            }
            catch (error) {
                console.error('关闭病历窗口失败:', error);
                return { success: false, error: error instanceof Error ? error.message : '关闭窗口失败' };
            }
        });
        // 获取窗口状态
        electron_1.ipcMain.handle('medical-record-window:get-state', async (event, windowId) => {
            const state = this.windowStates.get(windowId);
            return { success: true, state };
        });
        // 更新窗口状态
        electron_1.ipcMain.handle('medical-record-window:update-state', async (event, windowId, updates) => {
            try {
                this.updateWindowState(windowId, updates);
                return { success: true };
            }
            catch (error) {
                console.error('更新窗口状态失败:', error);
                return { success: false, error: error instanceof Error ? error.message : '更新状态失败' };
            }
        });
        // 设置窗口置顶
        electron_1.ipcMain.handle('medical-record-window:set-always-on-top', async (event, windowId, alwaysOnTop) => {
            try {
                const window = this.windows.get(windowId);
                if (window && !window.isDestroyed()) {
                    window.setAlwaysOnTop(alwaysOnTop);
                    this.updateWindowState(windowId, { isAlwaysOnTop: alwaysOnTop });
                }
                return { success: true };
            }
            catch (error) {
                console.error('设置窗口置顶失败:', error);
                return { success: false, error: error instanceof Error ? error.message : '设置置顶失败' };
            }
        });
    }
    /**
     * 获取所有窗口状态
     */
    getAllWindowStates() {
        return Array.from(this.windowStates.values());
    }
    /**
     * 关闭所有病历窗口
     */
    closeAllWindows() {
        for (const [windowId, window] of this.windows) {
            if (!window.isDestroyed()) {
                window.close();
            }
        }
    }
    /**
     * 清理资源
     */
    cleanup() {
        this.closeAllWindows();
        this.windows.clear();
        this.windowStates.clear();
    }
}
exports.MedicalRecordWindowManager = MedicalRecordWindowManager;
