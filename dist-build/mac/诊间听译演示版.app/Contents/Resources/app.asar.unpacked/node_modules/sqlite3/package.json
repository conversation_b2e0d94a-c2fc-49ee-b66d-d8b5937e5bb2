{"name": "sqlite3", "description": "Asynchronous, non-blocking SQLite3 bindings", "version": "5.1.7", "homepage": "https://github.com/TryGhost/node-sqlite3", "author": {"name": "Mapbox", "url": "https://mapbox.com/"}, "binary": {"napi_versions": [3, 6]}, "files": ["binding.gyp", "deps/", "lib/*.js", "lib/*.d.ts", "src/"], "repository": {"type": "git", "url": "https://github.com/TryGhost/node-sqlite3.git"}, "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1", "tar": "^6.1.11"}, "devDependencies": {"eslint": "8.56.0", "mocha": "10.2.0", "prebuild": "12.1.0"}, "peerDependencies": {"node-gyp": "8.x"}, "peerDependenciesMeta": {"node-gyp": {"optional": true}}, "optionalDependencies": {"node-gyp": "8.x"}, "license": "BSD-3-<PERSON><PERSON>", "main": "./lib/sqlite3", "types": "./lib/sqlite3.d.ts", "renovate": {"extends": ["@tryghost:base"]}}