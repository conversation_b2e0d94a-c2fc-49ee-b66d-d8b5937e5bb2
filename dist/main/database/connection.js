"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const sqlite3 = __importStar(require("sqlite3"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
const fs = __importStar(require("fs"));
class DatabaseManager {
    constructor() {
        this.db = null;
        // 确保用户数据目录存在
        const userDataPath = electron_1.app.getPath('userData');
        if (!fs.existsSync(userDataPath)) {
            fs.mkdirSync(userDataPath, { recursive: true });
        }
        this.dbPath = path.join(userDataPath, 'dental-transcription.db');
    }
    async initialize() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('数据库连接失败:', err);
                    reject(err);
                }
                else {
                    console.log('数据库连接成功:', this.dbPath);
                    this.runMigrations()
                        .then(() => resolve())
                        .catch(reject);
                }
            });
        });
    }
    async runMigrations() {
        const migrations = [
            // 用户表
            `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'doctor',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )`,
            // 录音记录表
            `CREATE TABLE IF NOT EXISTS recordings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        duration INTEGER,
        file_size INTEGER,
        status VARCHAR(20) DEFAULT 'completed',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`,
            // 转录记录表
            `CREATE TABLE IF NOT EXISTS transcriptions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recording_id INTEGER NOT NULL,
        transcription_text TEXT,
        confidence_score REAL,
        processing_time INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (recording_id) REFERENCES recordings(id)
      )`,
            // 病历记录表
            `CREATE TABLE IF NOT EXISTS medical_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transcription_id INTEGER NOT NULL,
        patient_info TEXT,
        chief_complaint TEXT,
        present_illness TEXT,
        examination TEXT,
        diagnosis TEXT,
        treatment_plan TEXT,
        medications TEXT,
        follow_up TEXT,
        notes TEXT,
        raw_llm_response TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transcription_id) REFERENCES transcriptions(id)
      )`,
            // 系统配置表
            `CREATE TABLE IF NOT EXISTS system_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
            // 用户会话表
            `CREATE TABLE IF NOT EXISTS user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`
        ];
        // 执行所有迁移
        for (const migration of migrations) {
            await this.execute(migration);
        }
        // 插入默认数据
        await this.insertDefaultData();
        // 插入演示数据
        await this.insertDemoData();
    }
    async insertDefaultData() {
        // 插入默认管理员用户（密码：admin123）
        const bcrypt = require('bcryptjs');
        const defaultPasswordHash = await bcrypt.hash('admin123', 10);
        await this.execute(`INSERT OR IGNORE INTO users (username, password_hash, role) VALUES (?, ?, ?)`, ['admin', defaultPasswordHash, 'admin']);
        // 插入默认配置
        const defaultConfigs = [
            ['asr_provider', 'alicloud'],
            ['llm_provider', 'openai'],
            ['recording_sample_rate', '16000'],
            ['recording_channels', '1'],
            ['recording_format', 'wav'],
            ['ui_theme', 'light'],
            ['ui_language', 'zh-CN'],
            ['auto_save', 'true']
        ];
        for (const [key, value] of defaultConfigs) {
            await this.execute(`INSERT OR IGNORE INTO system_config (config_key, config_value) VALUES (?, ?)`, [key, value]);
        }
    }
    async insertDemoData() {
        // 检查是否已经有演示数据
        const existingRecordings = await this.query('SELECT COUNT(*) as count FROM recordings');
        if (existingRecordings[0].count > 0) {
            return; // 已有数据，不插入演示数据
        }
        // 插入演示录音记录
        const demoRecordings = [
            {
                filename: '胸闷气短患者咨询.wav',
                duration: 180,
                transcription: '患者：医生您好，我最近总是感觉胸闷气短，特别是爬楼梯的时候。医生：您好，这种症状持续多长时间了？患者：大概有一个星期了，而且晚上睡觉的时候也会有点憋气的感觉。医生：您之前有高血压或者心脏病的病史吗？患者：我有高血压，一直在吃药控制，但是最近可能没有按时吃药。医生：明白了，我建议您先做个心电图和胸片检查，同时要规律服用降压药。'
            },
            {
                filename: '头痛患者问诊.wav',
                duration: 150,
                transcription: '患者：医生，我这两天头痛得厉害，还有点恶心想吐。医生：头痛是什么性质的？是胀痛还是刺痛？患者：主要是胀痛，集中在前额这里，按压会更痛一些。医生：有发热吗？患者：没有发热，但是感觉很疲劳，工作也没精神。医生：最近工作压力大吗？睡眠怎么样？患者：确实最近加班比较多，睡眠不太好，经常熬夜。医生：这可能是紧张性头痛，建议您注意休息，我开点止痛药给您。'
            },
            {
                filename: '腰痛患者咨询.wav',
                duration: 120,
                transcription: '患者：医生，我的腰痛了一个星期了，特别是久坐之后站起来的时候。医生：疼痛有向腿部放射吗？患者：没有，就是腰部这里痛，弯腰的时候会加重。医生：您平时工作需要长时间坐着吗？患者：是的，我是程序员，每天都要坐8-10个小时。医生：这很可能是腰肌劳损，建议您平时多活动，避免久坐，我给您开一些消炎止痛的药物。'
            }
        ];
        for (let i = 0; i < demoRecordings.length; i++) {
            const recording = demoRecordings[i];
            // 插入录音记录
            const recordingResult = await this.execute(`INSERT INTO recordings (user_id, filename, file_path, duration, file_size, status, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?)`, [
                1, // admin用户ID
                recording.filename,
                `/demo/${recording.filename}`,
                recording.duration,
                1024 * 50, // 假设50KB文件大小
                'completed',
                new Date(Date.now() - (i + 1) * 24 * 60 * 60 * 1000).toISOString() // 过去几天的日期
            ]);
            // 插入转录记录
            await this.execute(`INSERT INTO transcriptions (recording_id, transcription_text, confidence, status, created_at)
         VALUES (?, ?, ?, ?, ?)`, [
                recordingResult.id,
                recording.transcription,
                0.95,
                'completed',
                new Date(Date.now() - (i + 1) * 24 * 60 * 60 * 1000).toISOString()
            ]);
            // 插入病历记录
            const medicalRecords = [
                {
                    // 胸闷气短患者
                    patientInfo: '张三，男，45岁',
                    chiefComplaint: '胸闷气短1周，活动后加重',
                    presentIllness: '患者1周前无明显诱因出现胸闷气短，活动后明显加重，夜间平卧时偶有憋气感。既往有高血压病史5年，平时服用降压药物，但近期服药不规律。无胸痛、心悸等症状。',
                    examination: '体温36.5℃，血压160/95mmHg，心率88次/分，呼吸20次/分。心界不大，心律齐，各瓣膜听诊区未闻及病理性杂音。双肺呼吸音清，未闻及干湿性啰音。',
                    diagnosis: '1. 高血压病 2级（极高危）\n2. 疑似心功能不全',
                    treatmentPlan: '1. 规律服用降压药物，监测血压变化\n2. 完善心脏彩超、心电图、胸片检查\n3. 低盐低脂饮食，适量运动\n4. 避免情绪激动和过度劳累',
                    medications: '1. 氨氯地平片 5mg 每日一次 口服\n2. 美托洛尔片 25mg 每日两次 口服\n3. 螺内酯片 20mg 每日一次 口服',
                    followUp: '1周后复诊，带齐检查结果，根据检查情况调整治疗方案。如出现胸痛、严重气促等症状请及时就诊。',
                    notes: '患者依从性需要加强，强调规律服药的重要性。建议家属监督用药。'
                },
                {
                    // 头痛患者
                    patientInfo: '李梅，女，32岁',
                    chiefComplaint: '头痛伴恶心2天',
                    presentIllness: '患者2天前开始出现头痛，以前额部为主，呈胀痛性质，伴有恶心，无呕吐。疼痛程度中等，按压时加重。近期工作压力较大，经常熬夜加班，睡眠质量差。无发热、视物模糊等症状。',
                    examination: '体温36.8℃，血压120/80mmHg，心率76次/分。神志清楚，精神稍差。颈项无抵抗，瞳孔等大等圆，对光反射存在。前额部轻压痛，余神经系统检查无异常。',
                    diagnosis: '1. 紧张性头痛\n2. 神经衰弱',
                    treatmentPlan: '1. 充分休息，调整作息时间\n2. 减轻工作压力，避免熬夜\n3. 药物对症治疗\n4. 心理疏导，放松训练',
                    medications: '1. 布洛芬缓释胶囊 0.3g 每日两次 口服\n2. 谷维素片 20mg 每日三次 口服\n3. 维生素B1片 10mg 每日三次 口服',
                    followUp: '3天后电话随访，如症状无缓解或加重请及时就诊。建议1周后门诊复查。',
                    notes: '患者工作压力较大，需要调整生活方式。建议适当运动，学习放松技巧。'
                },
                {
                    // 腰痛患者
                    patientInfo: '王强，男，28岁',
                    chiefComplaint: '腰痛1周，久坐后加重',
                    presentIllness: '患者1周前无明显诱因出现腰痛，以腰骶部为主，久坐后疼痛加重，活动后稍有缓解。无下肢放射痛，无大小便异常。工作需长时间坐位，平时缺乏运动。',
                    examination: '腰椎生理弯曲存在，腰4-5棘突间压痛明显，腰部肌肉紧张。直腿抬高试验阴性，双下肢肌力、肌张力正常，感觉无异常。',
                    diagnosis: '腰肌劳损',
                    treatmentPlan: '1. 避免久坐，每小时起身活动5-10分钟\n2. 局部热敷，每日2-3次\n3. 腰背肌功能锻炼\n4. 药物治疗缓解症状',
                    medications: '1. 双氯芬酸钠缓释片 75mg 每日一次 口服\n2. 甲钴胺片 0.5mg 每日三次 口服\n3. 外用：扶他林乳胶剂 局部涂抹 每日2-3次',
                    followUp: '1周后复诊，如症状加重或出现下肢放射痛请及时就诊。',
                    notes: '建议改善工作姿势，加强腰背肌锻炼，避免长时间保持同一姿势。'
                }
            ];
            if (i < medicalRecords.length) {
                const record = medicalRecords[i];
                await this.execute(`INSERT INTO medical_records (
            transcription_id, patient_info, chief_complaint, present_illness,
            examination, diagnosis, treatment_plan, medications, follow_up, notes,
            raw_llm_response, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
                    recordingResult.id,
                    record.patientInfo,
                    record.chiefComplaint,
                    record.presentIllness,
                    record.examination,
                    record.diagnosis,
                    record.treatmentPlan,
                    record.medications,
                    record.followUp,
                    record.notes,
                    JSON.stringify(record), // 原始LLM响应
                    new Date(Date.now() - (i + 1) * 24 * 60 * 60 * 1000).toISOString()
                ]);
            }
        }
    }
    async execute(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }
            this.db.run(sql, params, function (err) {
                if (err) {
                    console.error('SQL执行错误:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                }
                else {
                    resolve({
                        id: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }
    async query(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('SQL查询错误:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                }
                else {
                    resolve(rows);
                }
            });
        });
    }
    async queryOne(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('SQL查询错误:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                }
                else {
                    resolve(row);
                }
            });
        });
    }
    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        reject(err);
                    }
                    else {
                        this.db = null;
                        resolve();
                    }
                });
            }
            else {
                resolve();
            }
        });
    }
    // 获取数据库路径
    getDatabasePath() {
        return this.dbPath;
    }
    // 检查数据库连接状态
    isConnected() {
        return this.db !== null;
    }
    // 录音相关方法
    async saveRecording(recordingData) {
        // 首先保存录音记录，让数据库自动生成ID
        const result = await this.execute(`INSERT INTO recordings (user_id, filename, file_path, duration, file_size, status, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`, [
            1, // 临时使用默认用户ID
            recordingData.filename,
            recordingData.path,
            Math.round(recordingData.duration),
            recordingData.size,
            'completed',
            recordingData.createdAt
        ]);
        const recordingId = result.lastID;
        // 如果有转录内容，保存转录记录
        if (recordingData.transcription) {
            await this.execute(`INSERT INTO transcriptions (recording_id, transcription_text, confidence_score, created_at)
         VALUES (?, ?, ?, ?)`, [
                recordingId,
                recordingData.transcription,
                0.8, // 默认置信度
                recordingData.createdAt
            ]);
        }
        return recordingId;
    }
    async getRecordings() {
        const sql = `
      SELECT
        r.id,
        r.filename,
        r.file_path as path,
        r.duration,
        r.file_size as size,
        r.status,
        r.created_at as createdAt,
        t.transcription_text as transcription,
        'audio/webm' as mimeType,
        16000 as sampleRate
      FROM recordings r
      LEFT JOIN transcriptions t ON r.id = t.recording_id
      ORDER BY r.created_at DESC
    `;
        return await this.query(sql);
    }
    async getRecording(id) {
        const sql = `
      SELECT
        r.id,
        r.filename,
        r.file_path as path,
        r.duration,
        r.file_size as size,
        r.status,
        r.created_at as createdAt,
        t.transcription_text as transcription,
        'audio/webm' as mimeType,
        16000 as sampleRate
      FROM recordings r
      LEFT JOIN transcriptions t ON r.id = t.recording_id
      WHERE r.id = ?
    `;
        return await this.queryOne(sql, [id]);
    }
    async deleteRecording(id) {
        // 删除转录记录
        await this.execute('DELETE FROM transcriptions WHERE recording_id = ?', [id]);
        // 删除录音记录
        await this.execute('DELETE FROM recordings WHERE id = ?', [id]);
    }
    async updateRecording(id, updates) {
        const fields = [];
        const values = [];
        if (updates.filename) {
            fields.push('filename = ?');
            values.push(updates.filename);
        }
        if (updates.path) {
            fields.push('file_path = ?');
            values.push(updates.path);
        }
        if (updates.status) {
            fields.push('status = ?');
            values.push(updates.status);
        }
        if (fields.length > 0) {
            values.push(id);
            await this.execute(`UPDATE recordings SET ${fields.join(', ')} WHERE id = ?`, values);
        }
        // 更新转录内容
        if (updates.transcription !== undefined) {
            const existing = await this.queryOne('SELECT id FROM transcriptions WHERE recording_id = ?', [id]);
            if (existing) {
                await this.execute('UPDATE transcriptions SET transcription_text = ? WHERE recording_id = ?', [updates.transcription, id]);
            }
            else {
                await this.execute('INSERT INTO transcriptions (recording_id, transcription_text, confidence_score, created_at) VALUES (?, ?, ?, ?)', [id, updates.transcription, 0.8, new Date().toISOString()]);
            }
        }
    }
    // 病历相关方法
    async saveMedicalRecord(data) {
        await this.execute(`INSERT INTO medical_records (
        transcription_id, patient_info, chief_complaint, present_illness,
        examination, diagnosis, treatment_plan, medications, follow_up,
        notes, raw_llm_response, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            data.transcriptionId,
            data.patientInfo,
            data.chiefComplaint,
            data.presentIllness,
            data.examination,
            data.diagnosis,
            data.treatmentPlan,
            data.medications,
            data.followUp,
            data.notes,
            data.rawLlmResponse,
            new Date().toISOString()
        ]);
    }
    async getMedicalRecord(transcriptionId) {
        return await this.queryOne('SELECT * FROM medical_records WHERE transcription_id = ?', [transcriptionId]);
    }
    async getAllMedicalRecords() {
        const sql = `
      SELECT
        mr.*,
        t.transcription_text,
        r.filename as recording_filename,
        r.created_at as recording_date
      FROM medical_records mr
      LEFT JOIN transcriptions t ON mr.transcription_id = t.id
      LEFT JOIN recordings r ON t.recording_id = r.id
      ORDER BY mr.created_at DESC
    `;
        return await this.query(sql);
    }
    async updateMedicalRecord(id, updates) {
        const fields = [];
        const values = [];
        for (const [key, value] of Object.entries(updates)) {
            if (value !== undefined) {
                // 转换驼峰命名为下划线命名
                const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();
                fields.push(`${dbField} = ?`);
                values.push(value);
            }
        }
        if (fields.length > 0) {
            values.push(id);
            await this.execute(`UPDATE medical_records SET ${fields.join(', ')} WHERE id = ?`, values);
        }
    }
    async deleteMedicalRecord(id) {
        await this.execute('DELETE FROM medical_records WHERE id = ?', [id]);
    }
    // 认证相关方法
    async getUserByUsername(username) {
        return await this.queryOne('SELECT * FROM users WHERE username = ?', [username]);
    }
    async getUserById(id) {
        return await this.queryOne('SELECT id, username, role, created_at, last_login FROM users WHERE id = ?', [id]);
    }
    async updateLastLogin(userId) {
        await this.execute('UPDATE users SET last_login = ? WHERE id = ?', [new Date().toISOString(), userId]);
    }
    async createSession(userId, sessionToken, expiresAt) {
        await this.execute('INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)', [userId, sessionToken, expiresAt.toISOString()]);
    }
    async getSessionByToken(sessionToken) {
        return await this.queryOne(`SELECT s.*, u.id as user_id, u.username, u.role
       FROM user_sessions s
       JOIN users u ON s.user_id = u.id
       WHERE s.session_token = ? AND s.expires_at > ?`, [sessionToken, new Date().toISOString()]);
    }
    async deleteSession(sessionToken) {
        await this.execute('DELETE FROM user_sessions WHERE session_token = ?', [sessionToken]);
    }
    async cleanExpiredSessions() {
        await this.execute('DELETE FROM user_sessions WHERE expires_at <= ?', [new Date().toISOString()]);
    }
}
exports.DatabaseManager = DatabaseManager;
