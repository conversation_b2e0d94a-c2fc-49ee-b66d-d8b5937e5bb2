"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcrypt = __importStar(require("bcryptjs"));
const crypto = __importStar(require("crypto"));
class AuthService {
    constructor(databaseManager) {
        this.SESSION_DURATION = 24 * 60 * 60 * 1000; // 24小时
        this.db = databaseManager;
    }
    /**
     * 用户登录
     */
    async login(credentials) {
        try {
            // 查找用户
            const user = await this.db.getUserByUsername(credentials.username);
            if (!user) {
                console.log('用户不存在:', credentials.username);
                return null;
            }
            // 验证密码
            const isPasswordValid = await bcrypt.compare(credentials.password, user.password_hash);
            if (!isPasswordValid) {
                console.log('密码错误:', credentials.username);
                return null;
            }
            // 生成会话令牌
            const sessionToken = this.generateSessionToken();
            const expiresAt = new Date(Date.now() + this.SESSION_DURATION);
            // 保存会话
            await this.db.createSession(user.id, sessionToken, expiresAt);
            // 更新最后登录时间
            await this.db.updateLastLogin(user.id);
            // 返回用户信息（不包含密码）
            const userInfo = {
                id: user.id,
                username: user.username,
                role: user.role,
                created_at: user.created_at,
                last_login: new Date().toISOString()
            };
            console.log('用户登录成功:', credentials.username);
            return {
                sessionToken,
                user: userInfo,
                expiresAt
            };
        }
        catch (error) {
            console.error('登录失败:', error);
            return null;
        }
    }
    /**
     * 验证会话
     */
    async validateSession(sessionToken) {
        try {
            const session = await this.db.getSessionByToken(sessionToken);
            if (!session) {
                return null;
            }
            return {
                id: session.user_id,
                username: session.username,
                role: session.role,
                created_at: session.created_at,
                last_login: session.last_login
            };
        }
        catch (error) {
            console.error('会话验证失败:', error);
            return null;
        }
    }
    /**
     * 用户登出
     */
    async logout(sessionToken) {
        try {
            await this.db.deleteSession(sessionToken);
            console.log('用户登出成功');
            return true;
        }
        catch (error) {
            console.error('登出失败:', error);
            return false;
        }
    }
    /**
     * 获取当前用户信息
     */
    async getCurrentUser(sessionToken) {
        return await this.validateSession(sessionToken);
    }
    /**
     * 清理过期会话
     */
    async cleanExpiredSessions() {
        try {
            await this.db.cleanExpiredSessions();
            console.log('过期会话清理完成');
        }
        catch (error) {
            console.error('清理过期会话失败:', error);
        }
    }
    /**
     * 生成会话令牌
     */
    generateSessionToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    /**
     * 创建新用户（管理员功能）
     */
    async createUser(userData) {
        try {
            // 检查用户名是否已存在
            const existingUser = await this.db.getUserByUsername(userData.username);
            if (existingUser) {
                console.log('用户名已存在:', userData.username);
                return false;
            }
            // 加密密码
            const passwordHash = await bcrypt.hash(userData.password, 10);
            // 创建用户
            await this.db.execute('INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)', [userData.username, passwordHash, userData.role || 'doctor']);
            console.log('用户创建成功:', userData.username);
            return true;
        }
        catch (error) {
            console.error('创建用户失败:', error);
            return false;
        }
    }
    /**
     * 修改密码
     */
    async changePassword(userId, oldPassword, newPassword) {
        try {
            // 获取用户信息
            const user = await this.db.queryOne('SELECT * FROM users WHERE id = ?', [userId]);
            if (!user) {
                return false;
            }
            // 验证旧密码
            const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password_hash);
            if (!isOldPasswordValid) {
                return false;
            }
            // 加密新密码
            const newPasswordHash = await bcrypt.hash(newPassword, 10);
            // 更新密码
            await this.db.execute('UPDATE users SET password_hash = ? WHERE id = ?', [newPasswordHash, userId]);
            console.log('密码修改成功, 用户ID:', userId);
            return true;
        }
        catch (error) {
            console.error('修改密码失败:', error);
            return false;
        }
    }
}
exports.AuthService = AuthService;
