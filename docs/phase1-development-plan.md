# 诊间听译产品 - 第一阶段开发计划

## 项目概述

### 产品定位
专门为口腔科诊间设计的智能听译系统，通过AI技术将医患沟通自动转化为结构化电子病历。

### MVP版本目标
实现核心功能的最小可行产品，包括基础录音控制、简单登录认证、单一云服务集成和基础病历生成。

## 技术架构设计

### 技术选型

#### 前端框架
- **主框架**: Electron + React + TypeScript
- **UI组件库**: Ant Design
- **状态管理**: Redux Toolkit
- **路由**: React Router
- **样式**: Styled Components + CSS Modules

#### 后端服务
- **本地存储**: SQLite
- **音频处理**: Web Audio API + MediaRecorder
- **网络请求**: Axios
- **加密**: crypto-js

#### 云服务集成
- **ASR服务**: 阿里云语音识别（初期单一服务）
- **LLM服务**: OpenAI GPT-3.5-turbo（初期单一服务）

### 系统架构

```
诊间听译客户端
├── 主进程 (Electron Main Process)
│   ├── 窗口管理
│   ├── 文件系统操作
│   ├── 系统集成
│   └── 安全控制
├── 渲染进程 (Electron Renderer Process)
│   ├── React应用
│   ├── 用户界面
│   ├── 业务逻辑
│   └── 状态管理
└── 数据层
    ├── 本地数据库 (SQLite)
    ├── 音频文件存储
    ├── 配置文件
    └── 缓存管理
```

### 模块设计

#### 1. 用户认证模块 (Auth Module)
```typescript
interface AuthModule {
  login(username: string, password: string): Promise<AuthResult>
  logout(): void
  getCurrentUser(): User | null
  isAuthenticated(): boolean
}
```

#### 2. 录音控制模块 (Recording Module)
```typescript
interface RecordingModule {
  startRecording(): Promise<void>
  pauseRecording(): void
  stopRecording(): Promise<AudioFile>
  getRecordingStatus(): RecordingStatus
  deleteRecording(id: string): Promise<void>
}
```

#### 3. 云服务集成模块 (Cloud Service Module)
```typescript
interface CloudServiceModule {
  transcribeAudio(audioFile: AudioFile): Promise<TranscriptionResult>
  generateMedicalRecord(text: string): Promise<MedicalRecord>
  configureServices(config: ServiceConfig): void
}
```

#### 4. 病历生成模块 (Medical Record Module)
```typescript
interface MedicalRecordModule {
  generateRecord(transcription: string): Promise<MedicalRecord>
  formatRecord(record: MedicalRecord): string
  saveRecord(record: MedicalRecord): Promise<void>
}
```

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login DATETIME
);
```

### 录音记录表 (recordings)
```sql
CREATE TABLE recordings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  duration INTEGER, -- 秒
  file_size INTEGER, -- 字节
  status VARCHAR(20) DEFAULT 'recorded', -- recorded, transcribed, processed
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 转录记录表 (transcriptions)
```sql
CREATE TABLE transcriptions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  recording_id INTEGER NOT NULL,
  transcription_text TEXT,
  confidence_score REAL,
  processing_time INTEGER, -- 毫秒
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (recording_id) REFERENCES recordings(id)
);
```

### 病历记录表 (medical_records)
```sql
CREATE TABLE medical_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  transcription_id INTEGER NOT NULL,
  patient_info TEXT, -- JSON格式
  symptoms TEXT,
  diagnosis TEXT,
  treatment_plan TEXT,
  medications TEXT,
  follow_up TEXT,
  raw_llm_response TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (transcription_id) REFERENCES transcriptions(id)
);
```

### 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  config_key VARCHAR(100) UNIQUE NOT NULL,
  config_value TEXT,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 项目结构

```
dental-transcription-client/
├── public/                     # 静态资源
├── src/
│   ├── main/                   # Electron主进程
│   │   ├── main.ts
│   │   ├── preload.ts
│   │   └── database/
│   │       ├── connection.ts
│   │       ├── migrations/
│   │       └── models/
│   ├── renderer/               # React渲染进程
│   │   ├── components/         # 通用组件
│   │   ├── pages/             # 页面组件
│   │   │   ├── Login/
│   │   │   ├── Dashboard/
│   │   │   ├── Recording/
│   │   │   └── MedicalRecord/
│   │   ├── services/          # 业务服务
│   │   │   ├── auth.service.ts
│   │   │   ├── recording.service.ts
│   │   │   ├── cloud.service.ts
│   │   │   └── medical-record.service.ts
│   │   ├── store/             # Redux状态管理
│   │   │   ├── slices/
│   │   │   └── index.ts
│   │   ├── utils/             # 工具函数
│   │   ├── types/             # TypeScript类型定义
│   │   ├── App.tsx
│   │   └── index.tsx
│   └── shared/                # 共享代码
│       ├── constants/
│       ├── interfaces/
│       └── utils/
├── docs/                      # 文档
├── tests/                     # 测试文件
├── package.json
├── tsconfig.json
├── webpack.config.js
└── README.md
```

## 开发计划

### 第1周：项目初始化与基础框架
- [x] 需求分析与技术架构设计
- [ ] 创建Electron + React项目
- [ ] 配置TypeScript和构建工具
- [ ] 搭建基础UI框架和路由
- [ ] 初始化数据库结构

### 第2周：用户认证模块
- [ ] 设计登录界面
- [ ] 实现用户认证逻辑
- [ ] 添加会话管理
- [ ] 实现密码加密存储

### 第3周：录音控制模块
- [ ] 实现音频采集功能
- [ ] 添加录音控制界面
- [ ] 实现录音文件管理
- [ ] 添加录音状态监控

### 第4周：云服务集成
- [ ] 集成阿里云ASR服务
- [ ] 集成OpenAI GPT服务
- [ ] 实现音频上传和转录
- [ ] 添加错误处理机制

### 第5周：病历生成模块
- [ ] 设计病历模板
- [ ] 实现LLM文本处理
- [ ] 添加病历格式化功能
- [ ] 实现病历保存和展示

### 第6周：系统集成与测试
- [ ] 整合各模块功能
- [ ] 进行端到端测试
- [ ] 修复发现的问题
- [ ] 性能优化

### 第7周：文档与部署
- [ ] 完善开发文档
- [ ] 编写用户手册
- [ ] 准备部署配置
- [ ] 创建安装包

## 关键技术实现要点

### 1. 音频录制实现
```typescript
class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];

  async startRecording(): Promise<void> {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    this.mediaRecorder = new MediaRecorder(stream);
    
    this.mediaRecorder.ondataavailable = (event) => {
      this.audioChunks.push(event.data);
    };
    
    this.mediaRecorder.start();
  }

  stopRecording(): Promise<Blob> {
    return new Promise((resolve) => {
      if (this.mediaRecorder) {
        this.mediaRecorder.onstop = () => {
          const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
          this.audioChunks = [];
          resolve(audioBlob);
        };
        this.mediaRecorder.stop();
      }
    });
  }
}
```

### 2. 云服务API调用
```typescript
class CloudService {
  async transcribeAudio(audioFile: File): Promise<string> {
    // 阿里云ASR API调用实现
    const formData = new FormData();
    formData.append('audio', audioFile);
    
    const response = await axios.post('/api/asr/transcribe', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    
    return response.data.transcription;
  }

  async generateMedicalRecord(text: string): Promise<MedicalRecord> {
    // OpenAI GPT API调用实现
    const response = await axios.post('/api/llm/generate', {
      prompt: this.buildMedicalPrompt(text),
      model: 'gpt-3.5-turbo'
    });
    
    return this.parseMedicalRecord(response.data.content);
  }
}
```

## 质量保证

### 测试策略
- **单元测试**: Jest + React Testing Library
- **集成测试**: 模块间接口测试
- **端到端测试**: Playwright
- **性能测试**: 音频处理性能监控

### 代码质量
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript严格模式
- **代码审查**: 关键功能代码审查
- **文档**: JSDoc注释

## 风险评估与应对

### 技术风险
1. **音频质量问题**: 实现噪音检测和质量监控
2. **网络连接问题**: 添加重试机制和离线缓存
3. **API限制**: 实现请求频率控制和错误处理

### 业务风险
1. **数据安全**: 实现本地加密存储
2. **用户体验**: 持续收集反馈并优化
3. **性能问题**: 实现异步处理和进度提示

## 下一阶段准备

### 技术债务记录
- 单一云服务限制（需要在第二阶段支持多服务配置）
- 简单的用户认证（需要增强安全性）
- 基础病历模板（需要专业化定制）

### 扩展接口预留
- 云服务配置接口
- 自定义提示词接口
- 数据导出接口
- 系统集成接口

## 相关文档

- [技术架构设计](./technical-architecture.md) - 详细的系统架构和模块设计
- [API设计文档](./api-design.md) - 完整的API接口规范
- [开发环境配置](./development-setup.md) - 开发环境搭建指南

## 开发进度跟踪

### 当前状态
- [x] 产品需求分析与技术架构设计
- [ ] 项目初始化与基础框架搭建
- [ ] 用户认证模块开发
- [ ] 录音控制模块开发
- [ ] 云服务集成模块开发
- [ ] 病历生成模块开发
- [ ] 系统集成与测试
- [ ] 文档整理与部署准备

### 里程碑
- **Week 1**: 完成项目初始化和基础框架
- **Week 2**: 完成用户认证功能
- **Week 3**: 完成录音控制功能
- **Week 4**: 完成云服务集成
- **Week 5**: 完成病历生成功能
- **Week 6**: 完成系统集成测试
- **Week 7**: 完成文档和部署准备

---

*本文档将在开发过程中持续更新，记录实际实现细节和遇到的问题解决方案。*
