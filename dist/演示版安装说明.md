# 诊间听译演示版 - 安装说明

## 📦 安装包信息

- **文件名**: 诊间听译演示版-1.0.0-arm64.dmg
- **文件大小**: 236MB
- **适用系统**: macOS (Apple Silicon - M1/M2/M3 芯片)
- **版本**: 1.0.0 演示版（已修复依赖问题 + 调试版本）

## 🚀 安装步骤

### 1. 安装应用
1. 双击 `诊间听译演示版-1.0.0-arm64.dmg` 文件
2. 将"诊间听译演示版"应用拖拽到"Applications"文件夹
3. 等待复制完成

### 2. 首次运行
1. 在 Launchpad 或 Applications 文件夹中找到"诊间听译演示版"
2. 右键点击应用，选择"打开"（首次运行需要这样做）
3. 在弹出的安全提示中点击"打开"

## 🔐 默认登录信息

- **用户名**: admin
- **密码**: admin123

## 🎯 演示功能

此演示版包含以下功能：

- ✅ **用户认证系统** - 登录/登出功能
- ✅ **录音管理** - 查看和管理录音文件
- ✅ **病历生成** - 包含演示数据的病历管理
- ✅ **系统设置** - 基本配置选项
- ✅ **现代化界面** - 基于 Ant Design 的用户界面

## ⚠️ 注意事项

1. **系统要求**: macOS 10.15 或更高版本
2. **芯片要求**: 仅适用于 Apple Silicon Mac (M1/M2/M3)
3. **演示限制**:
   - 云服务功能使用模拟数据
   - 语音转录功能为演示模式
   - 数据仅存储在本地
4. **安全设置**: 首次运行可能需要在"系统偏好设置 > 安全性与隐私"中允许应用运行
5. **修复说明**: 此版本已修复之前的 JavaScript 依赖错误，现在可以正常启动
6. **调试功能**: 此版本包含调试信息，启动时会先显示测试组件，然后加载完整应用

## 🛠️ 技术特性

- **框架**: Electron + React + TypeScript
- **UI 组件**: Ant Design
- **数据存储**: SQLite 本地数据库
- **状态管理**: Redux Toolkit
- **路由**: React Router
- **样式**: Styled Components

## 📞 技术支持

如遇到安装或使用问题，请联系技术支持。

---

**版本**: 1.0.0 演示版  
**构建日期**: 2025-08-03  
**支持平台**: macOS (Apple Silicon)
