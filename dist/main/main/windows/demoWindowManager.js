"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DemoWindowManager = void 0;
const electron_1 = require("electron");
const path = __importStar(require("path"));
class DemoWindowManager {
    constructor() {
        this.demoWindows = new Map();
        this.isDev = process.env.NODE_ENV === 'development';
        this.setupIpcHandlers();
    }
    static getInstance() {
        if (!DemoWindowManager.instance) {
            DemoWindowManager.instance = new DemoWindowManager();
        }
        return DemoWindowManager.instance;
    }
    setupIpcHandlers() {
        // 创建演示窗口
        electron_1.ipcMain.handle('demo-window:create', async (event) => {
            try {
                console.log('收到创建演示窗口请求');
                const parentWindow = electron_1.BrowserWindow.fromWebContents(event.sender);
                if (!parentWindow) {
                    throw new Error('无法获取父窗口');
                }
                const windowId = await this.createDemoWindow(parentWindow);
                console.log('演示窗口创建完成，窗口ID:', windowId);
                return { success: true, windowId };
            }
            catch (error) {
                console.error('创建演示窗口失败:', error);
                return { success: false, error: error instanceof Error ? error.message : '创建窗口失败' };
            }
        });
        // 关闭演示窗口
        electron_1.ipcMain.handle('demo-window:close', async (event, windowId) => {
            try {
                const window = this.demoWindows.get(windowId);
                if (window && !window.isDestroyed()) {
                    window.close();
                    this.demoWindows.delete(windowId);
                }
                return { success: true };
            }
            catch (error) {
                console.error('关闭演示窗口失败:', error);
                return { success: false, error: error instanceof Error ? error.message : '关闭窗口失败' };
            }
        });
    }
    async createDemoWindow(parentWindow) {
        const windowId = `demo-${Date.now()}`;
        // 获取父窗口位置
        const [parentX, parentY] = parentWindow.getPosition();
        const [parentWidth] = parentWindow.getSize();
        console.log('创建演示窗口，父窗口位置:', { parentX, parentY, parentWidth });
        // 创建新窗口
        const window = new electron_1.BrowserWindow({
            width: 600,
            height: 400,
            x: parentX + parentWidth + 20, // 在父窗口右侧
            y: parentY,
            title: '演示窗口',
            resizable: true,
            minimizable: true,
            maximizable: true,
            closable: true,
            alwaysOnTop: false,
            show: false, // 先不显示，等页面加载完成后再显示
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, '../preload.js')
            }
        });
        // 存储窗口引用
        this.demoWindows.set(windowId, window);
        // 窗口关闭时清理
        window.on('closed', () => {
            console.log('演示窗口已关闭:', windowId);
            this.demoWindows.delete(windowId);
        });
        // 加载页面
        console.log('开始加载演示窗口页面...');
        if (this.isDev) {
            console.log('开发模式：加载URL http://localhost:3000/demo');
            await window.loadURL('http://localhost:3000/demo');
        }
        else {
            console.log('生产模式：加载本地文件');
            await window.loadFile(path.join(__dirname, '../renderer/demo.html'));
        }
        console.log('演示窗口页面加载完成');
        // 页面加载完成后显示窗口
        window.webContents.once('did-finish-load', () => {
            console.log('演示窗口页面加载完成，显示窗口...');
            window.show();
            console.log('演示窗口已显示');
        });
        return windowId;
    }
    closeAllWindows() {
        this.demoWindows.forEach((window, windowId) => {
            if (!window.isDestroyed()) {
                window.close();
            }
        });
        this.demoWindows.clear();
    }
}
exports.DemoWindowManager = DemoWindowManager;
