#!/bin/bash

# DMG Verification Script
# This script verifies the integrity of the generated DMG file

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if DMG file exists
DMG_DIR="dist-build"
DMG_FILE=$(find "$DMG_DIR" -name "*.dmg" -type f | head -1)

if [ -z "$DMG_FILE" ]; then
    print_error "No DMG file found in $DMG_DIR"
    exit 1
fi

print_status "Found DMG file: $DMG_FILE"

# Verify DMG integrity
print_status "Verifying DMG integrity..."
if hdiutil verify "$DMG_FILE"; then
    print_status "✓ DMG integrity check passed"
else
    print_error "✗ DMG integrity check failed"
    exit 1
fi

# Test mounting the DMG
print_status "Testing DMG mount..."
MOUNT_OUTPUT=$(hdiutil attach "$DMG_FILE" -readonly -nobrowse)
MOUNT_POINT=$(echo "$MOUNT_OUTPUT" | grep -o '/Volumes/[^[:space:]]*' | tail -1)

if [ -z "$MOUNT_POINT" ]; then
    print_error "✗ Failed to mount DMG"
    exit 1
fi

print_status "✓ DMG mounted successfully at: $MOUNT_POINT"

# Check if the app exists in the mounted volume
APP_NAME="诊间听译演示版.app"
if [ -d "$MOUNT_POINT/$APP_NAME" ]; then
    print_status "✓ Application found in DMG: $APP_NAME"

    # Check app bundle structure
    if [ -f "$MOUNT_POINT/$APP_NAME/Contents/Info.plist" ]; then
        print_status "✓ App bundle structure is valid"
    else
        print_warning "⚠ App bundle structure may be incomplete"
    fi

    # Check executable (try different possible names)
    EXECUTABLE_FOUND=false
    for exec_name in "诊间听译演示版" "project" "Electron"; do
        if [ -f "$MOUNT_POINT/$APP_NAME/Contents/MacOS/$exec_name" ]; then
            print_status "✓ Main executable found: $exec_name"
            EXECUTABLE_FOUND=true
            break
        fi
    done

    if [ "$EXECUTABLE_FOUND" = false ]; then
        print_warning "⚠ Main executable not found with expected names"
        print_status "Available executables:"
        ls -la "$MOUNT_POINT/$APP_NAME/Contents/MacOS/" 2>/dev/null || true
    fi
else
    print_error "✗ Application not found in DMG"
    print_status "Contents of DMG:"
    ls -la "$MOUNT_POINT/" 2>/dev/null || true
    hdiutil detach "$MOUNT_POINT" 2>/dev/null || true
    exit 1
fi

# Check Applications symlink
if [ -L "$MOUNT_POINT/Applications" ]; then
    print_status "✓ Applications symlink found"
else
    print_warning "⚠ Applications symlink not found"
fi

# Unmount the DMG
print_status "Unmounting DMG..."
if hdiutil detach "$MOUNT_POINT"; then
    print_status "✓ DMG unmounted successfully"
else
    print_warning "⚠ Warning: Failed to unmount DMG cleanly"
fi

# Check file size (warn if too large)
DMG_SIZE=$(stat -f%z "$DMG_FILE" 2>/dev/null || stat -c%s "$DMG_FILE" 2>/dev/null)
DMG_SIZE_MB=$((DMG_SIZE / 1024 / 1024))

print_status "DMG file size: ${DMG_SIZE_MB}MB"

if [ $DMG_SIZE_MB -gt 500 ]; then
    print_warning "DMG file is quite large (${DMG_SIZE_MB}MB). Consider optimizing."
elif [ $DMG_SIZE_MB -gt 1000 ]; then
    print_error "DMG file is very large (${DMG_SIZE_MB}MB). This may cause download issues."
fi

print_status "✓ All DMG verification checks completed successfully!"
print_status "DMG file is ready for distribution: $DMG_FILE"
