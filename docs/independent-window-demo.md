# 独立病历窗口演示指南

## 演示流程

### 1. 启动应用
```bash
npm run dev
```

### 2. 打开小窗口
1. 在主应用中点击"小窗口"按钮
2. 主窗口隐藏，显示小窗口录音界面

### 3. 录音操作
1. 点击"▶"按钮开始录音
2. 说话进行语音录制（例如："患者主诉牙痛三天，疼痛剧烈"）
3. 点击"⏹"按钮停止录音

### 4. 生成独立病历窗口
1. 录音完成后，点击"生成"按钮
2. 系统显示"正在创建病历窗口..."提示
3. **新的独立病历窗口**在小窗口上方打开
4. 病历窗口开始生成内容（显示加载动画）

### 5. 查看病历内容
3秒后，病历窗口显示完整的结构化医疗记录：
- 患者信息：许慕希，女，28岁
- 主诉：牙痛3天
- 现病史：详细的症状描述
- 体格检查：检查结果
- 诊断：具体诊断结果
- 治疗方案：治疗建议
- 用药建议：药物处方
- 随访建议：后续计划
- 备注：医生备注

### 6. 编辑病历
1. 点击"编辑病历"按钮
2. 所有字段变为可编辑状态
3. 修改任意字段内容
4. 点击"完成编辑"保存修改

### 7. EMR系统集成演示
1. 点击"回传EMR系统"按钮
2. 显示"发送中..."状态
3. 2秒后显示"病历已成功发送到EMR系统！"提示

### 8. 窗口管理
1. **独立操作**：两个窗口可以独立操作
2. **调整大小**：病历窗口可以调整大小
3. **关闭窗口**：点击病历窗口的"×"按钮关闭
4. **继续录音**：小窗口录音功能不受影响

## 界面布局演示

### 初始状态（只有小窗口）
```
┌─────────────────────────────────────────────────┐
│ ⬅  许慕希    ● 未开始录音           ▶        │
└─────────────────────────────────────────────────┘
```

### 录音完成状态
```
┌─────────────────────────────────────────────────┐
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```

### 独立病历窗口打开后
```
┌─────────────────────────────────────────────────┐
│ ● 正在生成电子病历...    编辑病历 回传EMR ×    │
│ ┌─────────────────────────────────────────────┐ │
│ │              🔄                             │ │
│ │     AI正在分析您的录音转录内容，生成...        │ │
│ │                                             │ │
│ └─────────────────────────────────────────────┘ │
│                                                 │
│ [更多病历字段内容...]                            │
│                                                 │
└─────────────────────────────────────────────────┘
                    ↑ 10px 间距
┌─────────────────────────────────────────────────┐
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```

### 病历生成完成状态
```
┌─────────────────────────────────────────────────┐
│ ● 电子病历              编辑病历 回传EMR ×    │
│ ┌─────────────────────────────────────────────┐ │
│ │ 患者信息: 许慕希，女，28岁                   │ │
│ │ 主诉: 牙痛3天                              │ │
│ │ 现病史: 患者3天前无明显诱因出现右下后牙疼痛... │ │
│ │ 体格检查: 右下第一磨牙可见深龋洞，探痛明显... │ │
│ │ 诊断: 1. 右下第一磨牙急性牙髓炎              │ │
│ │      2. 深龋                              │ │
│ │ 治疗方案: 1. 急诊开髓引流，缓解疼痛          │ │
│ │          2. 完善根管治疗                   │ │
│ │ 用药建议: 1. 布洛芬缓释胶囊 0.3g bid po     │ │
│ │          2. 甲硝唑片 0.4g bid po          │ │
│ │ 随访建议: 1. 3天后复诊进行根管治疗           │ │
│ │          2. 如疼痛加重随时就诊              │ │
│ │ 备注: 患者疼痛明显，已告知注意事项            │ │
│ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────┘
                    ↑ 10px 间距
┌─────────────────────────────────────────────────┐
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```

## 关键特性演示

### 1. 窗口独立性
- ✅ 病历窗口与录音窗口完全独立
- ✅ 可以同时操作两个窗口
- ✅ 关闭病历窗口不影响录音功能

### 2. 智能定位
- ✅ 病历窗口自动显示在小窗口上方
- ✅ 10px间距避免窗口重叠
- ✅ 边界检查确保不超出屏幕

### 3. 窗口属性
- ✅ 始终置顶（alwaysOnTop）
- ✅ 可调整大小（resizable）
- ✅ 完整的窗口控制（最小化、最大化、关闭）

### 4. 数据传递
- ✅ 转录文本从小窗口传递到病历窗口
- ✅ 通过URL参数传递数据
- ✅ 支持中文内容的正确编码

### 5. 用户体验
- ✅ 流畅的窗口创建过程
- ✅ 清晰的状态提示
- ✅ 直观的操作反馈

## 技术验证点

### 1. IPC通信
```javascript
// 小窗口调用
await window.electronAPI.invoke('medical-record:create', transcription);

// 主进程处理
ipcMain.handle('medical-record:create', async (event, transcriptionText) => {
  this.createMedicalRecordWindow(transcriptionText || '');
});
```

### 2. 窗口定位计算
```javascript
// 获取小窗口位置
let miniWindowBounds = this.miniRecorderWindow?.getBounds();

// 计算病历窗口位置
const x = miniWindowBounds.x;
const y = Math.max(50, miniWindowBounds.y - windowHeight - margin);
```

### 3. 模式检测
```javascript
// URL参数解析
const urlParams = new URLSearchParams(window.location.search);
const mode = urlParams.get('mode');
const transcription = urlParams.get('transcription');

if (mode === 'medical-record') {
  setIsMedicalRecordMode(true);
  setMedicalRecordTranscription(decodeURIComponent(transcription || ''));
}
```

## 测试场景

### 场景1：基本功能测试
1. 启动应用 → 打开小窗口 → 录音 → 生成病历
2. 验证：病历窗口正确打开并显示内容

### 场景2：编辑功能测试
1. 打开病历窗口 → 点击编辑 → 修改内容 → 完成编辑
2. 验证：内容正确保存和显示

### 场景3：EMR集成测试
1. 打开病历窗口 → 点击回传EMR → 等待完成
2. 验证：显示成功提示

### 场景4：窗口管理测试
1. 打开病历窗口 → 调整大小 → 移动位置 → 关闭窗口
2. 验证：窗口操作正常，小窗口不受影响

### 场景5：多次操作测试
1. 录音 → 生成病历 → 关闭 → 再次录音 → 再次生成
2. 验证：可以重复操作，无内存泄漏

## 故障排除

### 问题1：病历窗口没有打开
- 检查控制台是否有错误信息
- 确认IPC通信是否正常
- 验证主进程窗口管理逻辑

### 问题2：窗口位置不正确
- 检查小窗口位置获取逻辑
- 验证坐标计算是否正确
- 确认屏幕边界检查

### 问题3：数据传递失败
- 检查URL参数编码
- 验证转录文本内容
- 确认模式检测逻辑

## 性能考虑

### 内存管理
- 窗口关闭时正确清理引用
- 避免内存泄漏
- 合理的窗口数量限制

### 响应性能
- 快速的窗口创建
- 流畅的内容加载
- 及时的用户反馈

这个独立病历窗口功能提供了完整的医疗记录管理体验，同时保持了录音功能的独立性和可用性。
