"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudServiceManager = void 0;
const fs_1 = __importDefault(require("fs"));
/**
 * 云服务管理器 - 演示版本
 * 提供音频转录和病历生成功能的模拟实现
 */
class CloudServiceManager {
    constructor() {
        this.config = null;
        this.loadConfig();
    }
    /**
     * 加载配置
     */
    loadConfig() {
        try {
            // 演示版使用默认配置
            this.config = {
                asr: {
                    provider: 'baidu',
                    apiKey: 'demo_key',
                    secretKey: 'demo_secret',
                    language: 'zh'
                },
                llm: {
                    provider: 'openai',
                    apiKey: 'demo_key',
                    model: 'gpt-3.5-turbo',
                    temperature: 0.7,
                    maxTokens: 2000
                }
            };
        }
        catch (error) {
            console.error('加载云服务配置失败:', error);
        }
    }
    /**
     * 更新配置
     */
    updateConfig(config) {
        this.config = config;
    }
    /**
     * 获取当前配置
     */
    getConfig() {
        return this.config;
    }
    /**
     * 音频转录 - 演示实现
     */
    async transcribeAudio(audioFilePath) {
        if (!this.config) {
            throw new Error('云服务配置未初始化');
        }
        // 检查文件是否存在
        if (!fs_1.default.existsSync(audioFilePath)) {
            throw new Error('音频文件不存在');
        }
        const startTime = Date.now();
        try {
            // 模拟转录过程
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
            // 演示数据
            const demoTexts = [
                '患者主诉头痛，持续时间约3天，伴有轻微发热。体格检查：体温37.5°C，血压正常，心率80次/分。建议进一步检查。',
                '患者因牙痛就诊，疼痛持续2天，夜间加重。口腔检查发现右下第一磨牙龋坏，叩痛阳性。诊断为急性牙髓炎。',
                '患者咳嗽咳痰1周，伴有低热。胸部听诊可闻及湿性啰音。胸片显示右下肺炎症改变。诊断为肺炎，建议抗感染治疗。'
            ];
            const randomText = demoTexts[Math.floor(Math.random() * demoTexts.length)];
            const result = {
                text: randomText,
                confidence: 0.85 + Math.random() * 0.1,
                processingTime: Date.now() - startTime,
                provider: this.config.asr.provider,
                segments: [
                    {
                        text: randomText,
                        startTime: 0,
                        endTime: 30,
                        confidence: 0.9
                    }
                ]
            };
            return result;
        }
        catch (error) {
            console.error('音频转录失败:', error);
            throw new Error(`音频转录失败: ${error.message}`);
        }
    }
    /**
     * 生成病历 - 演示实现
     */
    async generateMedicalRecord(transcriptionText, context) {
        if (!this.config) {
            throw new Error('云服务配置未初始化');
        }
        try {
            // 模拟病历生成过程
            await new Promise(resolve => setTimeout(resolve, 1500));
            const result = {
                patientInfo: `姓名：${context?.patientInfo?.name || '张三'}\n年龄：${context?.patientInfo?.age || '35'}岁\n性别：${context?.patientInfo?.gender || '男'}`,
                chiefComplaint: '头痛3天',
                presentIllness: '患者3天前无明显诱因出现头痛，呈持续性胀痛，伴有轻微发热，体温最高37.5°C，无恶心呕吐，无视物模糊。',
                examination: '体温37.5°C，血压120/80mmHg，心率80次/分，神志清楚，精神可，头颅无外伤，颈项无强直。',
                diagnosis: '1. 头痛待查\n2. 低热',
                treatmentPlan: '1. 完善血常规、头颅CT检查\n2. 对症治疗\n3. 观察病情变化',
                medications: '1. 布洛芬缓释胶囊 0.3g bid po\n2. 多饮水，注意休息',
                followUp: '3天后复诊，如症状加重随时就诊',
                notes: '患者配合度好，已告知注意事项'
            };
            return result;
        }
        catch (error) {
            console.error('病历生成失败:', error);
            throw new Error(`病历生成失败: ${error.message}`);
        }
    }
    /**
     * 测试连接
     */
    async testConnection() {
        try {
            if (!this.config) {
                return { success: false, message: '配置未初始化' };
            }
            await new Promise(resolve => setTimeout(resolve, 500));
            return { success: true, message: '连接测试成功（演示模式）' };
        }
        catch (error) {
            return { success: false, message: `连接测试失败: ${error.message}` };
        }
    }
    /**
     * 获取支持的语言列表
     */
    getSupportedLanguages() {
        return ['zh', 'zh-CN', 'en', 'en-US'];
    }
    /**
     * 获取支持的模型列表
     */
    getSupportedModels(provider) {
        switch (provider) {
            case 'openai':
                return ['whisper-1', 'gpt-3.5-turbo', 'gpt-4'];
            case 'baidu':
                return ['baidu-asr', 'baidu-llm'];
            case 'azure':
                return ['azure-stt', 'azure-openai'];
            default:
                return ['default'];
        }
    }
}
exports.CloudServiceManager = CloudServiceManager;
