# 诊间听译产品 - 开发环境配置指南

## 环境要求

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **Python**: 3.8+ (用于某些native模块编译)

### 开发工具推荐
- **IDE**: Visual Studio Code
- **数据库工具**: DB Browser for SQLite
- **API测试**: Postman 或 Insomnia
- **版本控制**: Git

## 项目初始化

### 1. 创建项目目录结构
```bash
mkdir dental-transcription-client
cd dental-transcription-client

# 创建基础目录结构
mkdir -p src/{main,renderer,shared}
mkdir -p src/renderer/{components,pages,services,store,utils,types}
mkdir -p src/main/{database,services}
mkdir -p docs tests public assets
```

### 2. 初始化package.json
```bash
npm init -y
```

### 3. 安装核心依赖

#### Electron相关
```bash
npm install --save electron
npm install --save-dev electron-builder concurrently wait-on
```

#### React相关
```bash
npm install --save react react-dom react-router-dom
npm install --save-dev @types/react @types/react-dom
```

#### UI组件库
```bash
npm install --save antd @ant-design/icons
npm install --save styled-components
npm install --save-dev @types/styled-components
```

#### 状态管理
```bash
npm install --save @reduxjs/toolkit react-redux
npm install --save-dev @types/react-redux
```

#### 工具库
```bash
npm install --save axios sqlite3 bcryptjs jsonwebtoken uuid
npm install --save-dev @types/bcryptjs @types/jsonwebtoken @types/uuid
```

#### 开发工具
```bash
npm install --save-dev typescript webpack webpack-cli webpack-dev-server
npm install --save-dev ts-loader css-loader style-loader file-loader
npm install --save-dev eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
npm install --save-dev prettier eslint-config-prettier eslint-plugin-prettier
npm install --save-dev jest @types/jest ts-jest @testing-library/react @testing-library/jest-dom
```

## 配置文件设置

### 1. TypeScript配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "src",
    "paths": {
      "@/*": ["*"],
      "@/components/*": ["renderer/components/*"],
      "@/pages/*": ["renderer/pages/*"],
      "@/services/*": ["renderer/services/*"],
      "@/store/*": ["renderer/store/*"],
      "@/utils/*": ["renderer/utils/*"],
      "@/types/*": ["renderer/types/*"],
      "@/shared/*": ["shared/*"]
    }
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ]
}
```

### 2. Webpack配置 (webpack.config.js)
```javascript
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
  mode: 'development',
  entry: './src/renderer/index.tsx',
  target: 'electron-renderer',
  devtool: 'source-map',
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/renderer/components'),
      '@/pages': path.resolve(__dirname, 'src/renderer/pages'),
      '@/services': path.resolve(__dirname, 'src/renderer/services'),
      '@/store': path.resolve(__dirname, 'src/renderer/store'),
      '@/utils': path.resolve(__dirname, 'src/renderer/utils'),
      '@/types': path.resolve(__dirname, 'src/renderer/types'),
      '@/shared': path.resolve(__dirname, 'src/shared')
    }
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/,
        use: {
          loader: 'file-loader',
          options: {
            name: '[path][name].[ext]'
          }
        }
      }
    ]
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html'
    })
  ],
  output: {
    filename: 'bundle.js',
    path: path.resolve(__dirname, 'dist/renderer')
  },
  devServer: {
    port: 3000,
    hot: true,
    historyApiFallback: true
  }
};
```

### 3. ESLint配置 (.eslintrc.js)
```javascript
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  settings: {
    react: {
      version: 'detect'
    }
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'prettier'
  ],
  plugins: [
    '@typescript-eslint',
    'react',
    'react-hooks',
    'prettier'
  ],
  rules: {
    'prettier/prettier': 'error',
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'error'
  },
  env: {
    browser: true,
    es6: true,
    node: true
  }
};
```

### 4. Prettier配置 (.prettierrc)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### 5. Jest配置 (jest.config.js)
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/renderer/components/$1',
    '^@/pages/(.*)$': '<rootDir>/src/renderer/pages/$1',
    '^@/services/(.*)$': '<rootDir>/src/renderer/services/$1',
    '^@/store/(.*)$': '<rootDir>/src/renderer/store/$1',
    '^@/utils/(.*)$': '<rootDir>/src/renderer/utils/$1',
    '^@/types/(.*)$': '<rootDir>/src/renderer/types/$1',
    '^@/shared/(.*)$': '<rootDir>/src/shared/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main/**/*',
    '!src/renderer/index.tsx'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  }
};
```

## 更新package.json脚本

```json
{
  "name": "dental-transcription-client",
  "version": "1.0.0",
  "description": "诊间听译客户端应用",
  "main": "dist/main/main.js",
  "homepage": "./",
  "scripts": {
    "dev": "concurrently \"npm run dev:renderer\" \"wait-on http://localhost:3000 && npm run dev:main\"",
    "dev:renderer": "webpack serve --config webpack.config.js",
    "dev:main": "tsc -p src/main/tsconfig.json && electron dist/main/main.js",
    "build": "npm run build:renderer && npm run build:main",
    "build:renderer": "webpack --mode production",
    "build:main": "tsc -p src/main/tsconfig.json",
    "build:electron": "npm run build && electron-builder",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.{ts,tsx}",
    "lint:fix": "eslint src/**/*.{ts,tsx} --fix",
    "format": "prettier --write src/**/*.{ts,tsx,css,md}",
    "type-check": "tsc --noEmit"
  },
  "build": {
    "appId": "com.dental.transcription",
    "productName": "诊间听译",
    "directories": {
      "output": "release"
    },
    "files": [
      "dist/**/*",
      "node_modules/**/*",
      "package.json"
    ],
    "mac": {
      "category": "public.app-category.medical"
    },
    "win": {
      "target": "nsis"
    },
    "linux": {
      "target": "AppImage"
    }
  },
  "keywords": [
    "electron",
    "react",
    "typescript",
    "medical",
    "transcription"
  ],
  "author": "Your Name",
  "license": "MIT"
}
```

## 环境变量配置

### 1. 创建环境变量文件
```bash
# .env.development
NODE_ENV=development
REACT_APP_API_BASE_URL=http://localhost:3001
REACT_APP_VERSION=1.0.0

# .env.production
NODE_ENV=production
REACT_APP_API_BASE_URL=https://api.dental-transcription.com
REACT_APP_VERSION=1.0.0
```

### 2. 云服务配置模板 (.env.example)
```bash
# 阿里云语音识别配置
ALICLOUD_ACCESS_KEY_ID=your_access_key_id
ALICLOUD_ACCESS_KEY_SECRET=your_access_key_secret
ALICLOUD_ASR_ENDPOINT=https://nls-gateway.cn-shanghai.aliyuncs.com

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 数据库配置
DATABASE_PATH=./data/dental-transcription.db

# 应用配置
APP_SECRET_KEY=your_secret_key_for_jwt
LOG_LEVEL=info
```

## 开发工具配置

### 1. VS Code配置 (.vscode/settings.json)
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.tsx": "typescriptreact",
    "*.ts": "typescript"
  },
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  }
}
```

### 2. VS Code推荐扩展 (.vscode/extensions.json)
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-jest"
  ]
}
```

## 数据库初始化

### 1. 创建数据库初始化脚本 (src/main/database/init.sql)
```sql
-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role VARCHAR(20) DEFAULT 'doctor',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login DATETIME
);

-- 录音记录表
CREATE TABLE IF NOT EXISTS recordings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  duration INTEGER,
  file_size INTEGER,
  status VARCHAR(20) DEFAULT 'completed',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 转录记录表
CREATE TABLE IF NOT EXISTS transcriptions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  recording_id INTEGER NOT NULL,
  transcription_text TEXT,
  confidence_score REAL,
  processing_time INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (recording_id) REFERENCES recordings(id)
);

-- 病历记录表
CREATE TABLE IF NOT EXISTS medical_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  transcription_id INTEGER NOT NULL,
  patient_info TEXT,
  chief_complaint TEXT,
  present_illness TEXT,
  examination TEXT,
  diagnosis TEXT,
  treatment_plan TEXT,
  medications TEXT,
  follow_up TEXT,
  notes TEXT,
  raw_llm_response TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (transcription_id) REFERENCES transcriptions(id)
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  config_key VARCHAR(100) UNIQUE NOT NULL,
  config_value TEXT,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认管理员用户
INSERT OR IGNORE INTO users (username, password_hash, role) 
VALUES ('admin', '$2b$10$rQZ8QqXqQqXqQqXqQqXqQu', 'admin');

-- 插入默认配置
INSERT OR IGNORE INTO system_config (config_key, config_value) VALUES
('asr_provider', 'alicloud'),
('llm_provider', 'openai'),
('recording_sample_rate', '16000'),
('recording_channels', '1'),
('ui_theme', 'light'),
('ui_language', 'zh-CN');
```

## 启动开发环境

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 运行测试
```bash
npm test
```

### 4. 代码检查
```bash
npm run lint
npm run type-check
```

## 常见问题解决

### 1. Node.js版本问题
```bash
# 使用nvm管理Node.js版本
nvm install 18
nvm use 18
```

### 2. SQLite编译问题
```bash
# 重新编译native模块
npm rebuild sqlite3
```

### 3. Electron启动问题
```bash
# 清理缓存
rm -rf node_modules
npm install
```

---

*本文档提供了完整的开发环境配置指南，确保团队成员能够快速搭建一致的开发环境。*
