# 诊间听译 - 部署指南

## 📋 目录

1. [环境准备](#环境准备)
2. [构建应用](#构建应用)
3. [打包发布](#打包发布)
4. [部署配置](#部署配置)
5. [自动更新](#自动更新)
6. [监控维护](#监控维护)

## 🛠️ 环境准备

### 开发环境要求

```bash
# Node.js 版本
node --version  # >= 18.0.0

# npm 版本
npm --version   # >= 8.0.0

# Python 版本（用于native模块编译）
python --version  # >= 3.8.0
```

### 系统依赖

#### Windows
```bash
# 安装 Windows Build Tools
npm install -g windows-build-tools

# 或者安装 Visual Studio Build Tools
# 下载并安装 Visual Studio Installer
# 选择 "C++ build tools" 工作负载
```

#### macOS
```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 安装 Homebrew（如果没有）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### Linux (Ubuntu/Debian)
```bash
# 安装构建依赖
sudo apt-get update
sudo apt-get install -y build-essential libnss3-dev libatk-bridge2.0-dev libdrm2 libxkbcommon-dev libxcomposite-dev libxdamage-dev libxrandr-dev libgbm-dev libxss1 libasound2-dev
```

## 🏗️ 构建应用

### 1. 克隆项目

```bash
git clone <repository-url>
cd dental-transcription-client
```

### 2. 安装依赖

```bash
# 安装项目依赖
npm install

# 清理缓存（如果遇到问题）
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

环境变量配置示例：
```env
# 应用配置
APP_NAME=诊间听译
APP_VERSION=1.0.0
NODE_ENV=production

# 数据库配置
DB_PATH=./data/app.db

# 日志配置
LOG_LEVEL=info
LOG_PATH=./logs

# 更新服务器配置
UPDATE_SERVER_URL=https://your-update-server.com
```

### 4. 构建前端

```bash
# 构建 React 应用
npm run build:renderer

# 验证构建结果
ls -la dist/renderer/
```

### 5. 构建主进程

```bash
# 构建 Electron 主进程
npm run build:main

# 验证构建结果
ls -la dist/main/
```

## 📦 打包发布

### 1. 配置打包参数

编辑 `package.json` 中的 `build` 配置：

```json
{
  "build": {
    "appId": "com.company.dental-transcription",
    "productName": "诊间听译",
    "directories": {
      "output": "release"
    },
    "files": [
      "dist/**/*",
      "node_modules/**/*",
      "package.json"
    ],
    "mac": {
      "category": "public.app-category.medical",
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        }
      ]
    },
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64", "ia32"]
        }
      ]
    },
    "linux": {
      "target": [
        {
          "target": "AppImage",
          "arch": ["x64"]
        }
      ]
    }
  }
}
```

### 2. 打包不同平台

#### Windows 打包
```bash
# 在 Windows 系统上执行
npm run build:win

# 或者交叉编译（在其他系统上）
npm run build:win -- --x64
```

#### macOS 打包
```bash
# 在 macOS 系统上执行
npm run build:mac

# 打包通用版本（Intel + Apple Silicon）
npm run build:mac -- --universal
```

#### Linux 打包
```bash
# 在 Linux 系统上执行
npm run build:linux

# 或者交叉编译
npm run build:linux -- --x64
```

#### 全平台打包
```bash
# 打包所有平台（需要在对应系统上执行）
npm run build:all
```

### 3. 验证打包结果

```bash
# 查看打包输出
ls -la release/

# 验证安装包
# Windows: 运行 .exe 文件
# macOS: 打开 .dmg 文件
# Linux: 运行 .AppImage 文件
```

## 🚀 部署配置

### 1. 服务器部署

#### 更新服务器搭建

```bash
# 创建更新服务器目录
mkdir update-server
cd update-server

# 创建简单的更新服务器
npm init -y
npm install express multer

# 创建服务器文件
cat > server.js << 'EOF'
const express = require('express');
const path = require('path');
const app = express();

app.use('/releases', express.static('releases'));

app.get('/update/:platform/:version', (req, res) => {
  const { platform, version } = req.params;
  // 检查是否有新版本
  // 返回更新信息
  res.json({
    version: '1.0.1',
    url: `/releases/latest-${platform}.zip`,
    releaseNotes: '修复了一些问题'
  });
});

app.listen(3000, () => {
  console.log('Update server running on port 3000');
});
EOF

# 启动更新服务器
node server.js
```

### 2. 应用配置

#### 数据库初始化

```bash
# 创建数据目录
mkdir -p data

# 初始化数据库
node scripts/init-database.js
```

#### 日志配置

```bash
# 创建日志目录
mkdir -p logs

# 配置日志轮转
cat > logrotate.conf << 'EOF'
logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

### 3. 系统服务配置

#### Windows 服务

```batch
@echo off
REM 创建 Windows 服务
sc create "DentalTranscription" binPath= "C:\Program Files\诊间听译\诊间听译.exe" start= auto
sc description "DentalTranscription" "诊间听译服务"
sc start "DentalTranscription"
```

#### macOS 服务 (LaunchAgent)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.company.dental-transcription</string>
    <key>ProgramArguments</key>
    <array>
        <string>/Applications/诊间听译.app/Contents/MacOS/诊间听译</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
</dict>
</plist>
```

#### Linux 服务 (systemd)

```ini
[Unit]
Description=诊间听译服务
After=network.target

[Service]
Type=simple
User=dental
WorkingDirectory=/opt/dental-transcription
ExecStart=/opt/dental-transcription/诊间听译
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 🔄 自动更新

### 1. 配置自动更新

```javascript
// main.ts 中配置
import { autoUpdater } from 'electron-updater';

// 配置更新服务器
autoUpdater.setFeedURL({
  provider: 'generic',
  url: 'https://your-update-server.com/releases'
});

// 检查更新
autoUpdater.checkForUpdatesAndNotify();

// 监听更新事件
autoUpdater.on('update-available', () => {
  console.log('发现新版本');
});

autoUpdater.on('update-downloaded', () => {
  console.log('更新下载完成');
  autoUpdater.quitAndInstall();
});
```

### 2. 发布更新

```bash
# 构建新版本
npm version patch  # 或 minor, major
npm run build:all

# 上传到更新服务器
scp release/* user@update-server:/path/to/releases/

# 更新版本信息
curl -X POST https://your-update-server.com/api/releases \
  -H "Content-Type: application/json" \
  -d '{"version": "1.0.1", "releaseNotes": "修复了一些问题"}'
```

## 📊 监控维护

### 1. 日志监控

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 日志分析
grep "ERROR" logs/app.log | tail -20
```

### 2. 性能监控

```javascript
// 添加性能监控
const { app } = require('electron');

// 监控内存使用
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  console.log('Memory usage:', memoryUsage);
}, 60000);

// 监控CPU使用
const os = require('os');
setInterval(() => {
  const cpuUsage = os.loadavg();
  console.log('CPU usage:', cpuUsage);
}, 60000);
```

### 3. 健康检查

```bash
#!/bin/bash
# health-check.sh

# 检查进程是否运行
if pgrep -f "诊间听译" > /dev/null; then
    echo "应用正在运行"
else
    echo "应用未运行，尝试重启"
    # 重启应用
    systemctl restart dental-transcription
fi

# 检查端口是否监听
if netstat -tuln | grep :3000 > /dev/null; then
    echo "端口 3000 正常监听"
else
    echo "端口 3000 未监听"
fi

# 检查磁盘空间
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "磁盘空间不足: ${DISK_USAGE}%"
fi
```

### 4. 备份策略

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/dental-transcription"
DATA_DIR="/opt/dental-transcription/data"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $DATA_DIR/app.db $BACKUP_DIR/app_$DATE.db

# 备份配置文件
cp $DATA_DIR/config.json $BACKUP_DIR/config_$DATE.json

# 压缩备份
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz $BACKUP_DIR/*_$DATE.*

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete

echo "备份完成: backup_$DATE.tar.gz"
```

## 🔒 安全配置

### 1. 证书签名

```bash
# Windows 代码签名
signtool sign /f certificate.p12 /p password /t http://timestamp.digicert.com release/诊间听译-Setup-1.0.0.exe

# macOS 代码签名
codesign --force --verify --verbose --sign "Developer ID Application: Your Name" release/诊间听译-1.0.0.dmg
```

### 2. 权限配置

```bash
# 设置文件权限
chmod 755 /opt/dental-transcription/诊间听译
chmod 644 /opt/dental-transcription/data/*
chmod 600 /opt/dental-transcription/config/secrets.json
```

## 📞 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本
   - 清理 node_modules 重新安装
   - 检查系统依赖

2. **打包失败**
   - 检查 electron-builder 配置
   - 确认所有文件路径正确
   - 检查代码签名证书

3. **运行时错误**
   - 查看应用日志
   - 检查权限设置
   - 验证配置文件

---

*本指南基于诊间听译 v1.0.0 版本编写，部署前请确保已完成充分测试。*
