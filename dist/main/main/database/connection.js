"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const sqlite3 = __importStar(require("sqlite3"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
const fs = __importStar(require("fs"));
class DatabaseManager {
    constructor() {
        this.db = null;
        // 确保用户数据目录存在
        const userDataPath = electron_1.app.getPath('userData');
        if (!fs.existsSync(userDataPath)) {
            fs.mkdirSync(userDataPath, { recursive: true });
        }
        this.dbPath = path.join(userDataPath, 'dental-transcription.db');
    }
    async initialize() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('数据库连接失败:', err);
                    reject(err);
                }
                else {
                    console.log('数据库连接成功:', this.dbPath);
                    this.runMigrations()
                        .then(() => resolve())
                        .catch(reject);
                }
            });
        });
    }
    async runMigrations() {
        const migrations = [
            // 用户表
            `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'doctor',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )`,
            // 录音记录表
            `CREATE TABLE IF NOT EXISTS recordings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        duration INTEGER,
        file_size INTEGER,
        status VARCHAR(20) DEFAULT 'completed',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`,
            // 转录记录表
            `CREATE TABLE IF NOT EXISTS transcriptions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recording_id INTEGER NOT NULL,
        transcription_text TEXT,
        confidence_score REAL,
        processing_time INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (recording_id) REFERENCES recordings(id)
      )`,
            // 病历记录表
            `CREATE TABLE IF NOT EXISTS medical_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transcription_id INTEGER NOT NULL,
        patient_info TEXT,
        chief_complaint TEXT,
        present_illness TEXT,
        examination TEXT,
        diagnosis TEXT,
        treatment_plan TEXT,
        medications TEXT,
        follow_up TEXT,
        notes TEXT,
        raw_llm_response TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transcription_id) REFERENCES transcriptions(id)
      )`,
            // 系统配置表
            `CREATE TABLE IF NOT EXISTS system_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
            // 用户会话表
            `CREATE TABLE IF NOT EXISTS user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`
        ];
        // 执行所有迁移
        for (const migration of migrations) {
            await this.execute(migration);
        }
        // 插入默认数据
        await this.insertDefaultData();
    }
    async insertDefaultData() {
        // 插入默认管理员用户（密码：admin123）
        const bcrypt = require('bcryptjs');
        const defaultPasswordHash = await bcrypt.hash('admin123', 10);
        await this.execute(`INSERT OR IGNORE INTO users (username, password_hash, role) VALUES (?, ?, ?)`, ['admin', defaultPasswordHash, 'admin']);
        // 插入默认配置
        const defaultConfigs = [
            ['asr_provider', 'alicloud'],
            ['llm_provider', 'openai'],
            ['recording_sample_rate', '16000'],
            ['recording_channels', '1'],
            ['recording_format', 'wav'],
            ['ui_theme', 'light'],
            ['ui_language', 'zh-CN'],
            ['auto_save', 'true']
        ];
        for (const [key, value] of defaultConfigs) {
            await this.execute(`INSERT OR IGNORE INTO system_config (config_key, config_value) VALUES (?, ?)`, [key, value]);
        }
    }
    async execute(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }
            this.db.run(sql, params, function (err) {
                if (err) {
                    console.error('SQL执行错误:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                }
                else {
                    resolve({
                        id: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }
    async query(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('SQL查询错误:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                }
                else {
                    resolve(rows);
                }
            });
        });
    }
    async queryOne(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('SQL查询错误:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                }
                else {
                    resolve(row);
                }
            });
        });
    }
    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        reject(err);
                    }
                    else {
                        this.db = null;
                        resolve();
                    }
                });
            }
            else {
                resolve();
            }
        });
    }
    // 获取数据库路径
    getDatabasePath() {
        return this.dbPath;
    }
    // 检查数据库连接状态
    isConnected() {
        return this.db !== null;
    }
    // 录音相关方法
    async saveRecording(recordingData) {
        // 首先保存录音记录，让数据库自动生成ID
        const result = await this.execute(`INSERT INTO recordings (user_id, filename, file_path, duration, file_size, status, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`, [
            1, // 临时使用默认用户ID
            recordingData.filename,
            recordingData.path,
            Math.round(recordingData.duration),
            recordingData.size,
            'completed',
            recordingData.createdAt
        ]);
        const recordingId = result.lastID;
        // 如果有转录内容，保存转录记录
        if (recordingData.transcription) {
            await this.execute(`INSERT INTO transcriptions (recording_id, transcription_text, confidence_score, created_at)
         VALUES (?, ?, ?, ?)`, [
                recordingId,
                recordingData.transcription,
                0.8, // 默认置信度
                recordingData.createdAt
            ]);
        }
        return recordingId;
    }
    async getRecordings() {
        const sql = `
      SELECT
        r.id,
        r.filename,
        r.file_path as path,
        r.duration,
        r.file_size as size,
        r.status,
        r.created_at as createdAt,
        t.transcription_text as transcription,
        'audio/webm' as mimeType,
        16000 as sampleRate
      FROM recordings r
      LEFT JOIN transcriptions t ON r.id = t.recording_id
      ORDER BY r.created_at DESC
    `;
        return await this.query(sql);
    }
    async getRecording(id) {
        const sql = `
      SELECT
        r.id,
        r.filename,
        r.file_path as path,
        r.duration,
        r.file_size as size,
        r.status,
        r.created_at as createdAt,
        t.transcription_text as transcription,
        'audio/webm' as mimeType,
        16000 as sampleRate
      FROM recordings r
      LEFT JOIN transcriptions t ON r.id = t.recording_id
      WHERE r.id = ?
    `;
        return await this.queryOne(sql, [id]);
    }
    async deleteRecording(id) {
        // 删除转录记录
        await this.execute('DELETE FROM transcriptions WHERE recording_id = ?', [id]);
        // 删除录音记录
        await this.execute('DELETE FROM recordings WHERE id = ?', [id]);
    }
    async updateRecording(id, updates) {
        const fields = [];
        const values = [];
        if (updates.filename) {
            fields.push('filename = ?');
            values.push(updates.filename);
        }
        if (updates.path) {
            fields.push('file_path = ?');
            values.push(updates.path);
        }
        if (updates.status) {
            fields.push('status = ?');
            values.push(updates.status);
        }
        if (fields.length > 0) {
            values.push(id);
            await this.execute(`UPDATE recordings SET ${fields.join(', ')} WHERE id = ?`, values);
        }
        // 更新转录内容
        if (updates.transcription !== undefined) {
            const existing = await this.queryOne('SELECT id FROM transcriptions WHERE recording_id = ?', [id]);
            if (existing) {
                await this.execute('UPDATE transcriptions SET transcription_text = ? WHERE recording_id = ?', [updates.transcription, id]);
            }
            else {
                await this.execute('INSERT INTO transcriptions (recording_id, transcription_text, confidence_score, created_at) VALUES (?, ?, ?, ?)', [id, updates.transcription, 0.8, new Date().toISOString()]);
            }
        }
    }
    // 病历相关方法
    async saveMedicalRecord(data) {
        await this.execute(`INSERT INTO medical_records (
        transcription_id, patient_info, chief_complaint, present_illness,
        examination, diagnosis, treatment_plan, medications, follow_up,
        notes, raw_llm_response, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            data.transcriptionId,
            data.patientInfo,
            data.chiefComplaint,
            data.presentIllness,
            data.examination,
            data.diagnosis,
            data.treatmentPlan,
            data.medications,
            data.followUp,
            data.notes,
            data.rawLlmResponse,
            new Date().toISOString()
        ]);
    }
    async getMedicalRecord(transcriptionId) {
        return await this.queryOne('SELECT * FROM medical_records WHERE transcription_id = ?', [transcriptionId]);
    }
    async getAllMedicalRecords() {
        const sql = `
      SELECT
        mr.*,
        t.transcription_text,
        r.filename as recording_filename,
        r.created_at as recording_date
      FROM medical_records mr
      LEFT JOIN transcriptions t ON mr.transcription_id = t.id
      LEFT JOIN recordings r ON t.recording_id = r.id
      ORDER BY mr.created_at DESC
    `;
        return await this.query(sql);
    }
    async updateMedicalRecord(id, updates) {
        const fields = [];
        const values = [];
        for (const [key, value] of Object.entries(updates)) {
            if (value !== undefined) {
                // 转换驼峰命名为下划线命名
                const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();
                fields.push(`${dbField} = ?`);
                values.push(value);
            }
        }
        if (fields.length > 0) {
            values.push(id);
            await this.execute(`UPDATE medical_records SET ${fields.join(', ')} WHERE id = ?`, values);
        }
    }
    async deleteMedicalRecord(id) {
        await this.execute('DELETE FROM medical_records WHERE id = ?', [id]);
    }
    // 认证相关方法
    async getUserByUsername(username) {
        return await this.queryOne('SELECT * FROM users WHERE username = ?', [username]);
    }
    async getUserById(id) {
        return await this.queryOne('SELECT id, username, role, created_at, last_login FROM users WHERE id = ?', [id]);
    }
    async updateLastLogin(userId) {
        await this.execute('UPDATE users SET last_login = ? WHERE id = ?', [new Date().toISOString(), userId]);
    }
    async createSession(userId, sessionToken, expiresAt) {
        await this.execute('INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)', [userId, sessionToken, expiresAt.toISOString()]);
    }
    async getSessionByToken(sessionToken) {
        return await this.queryOne(`SELECT s.*, u.id as user_id, u.username, u.role
       FROM user_sessions s
       JOIN users u ON s.user_id = u.id
       WHERE s.session_token = ? AND s.expires_at > ?`, [sessionToken, new Date().toISOString()]);
    }
    async deleteSession(sessionToken) {
        await this.execute('DELETE FROM user_sessions WHERE session_token = ?', [sessionToken]);
    }
    async cleanExpiredSessions() {
        await this.execute('DELETE FROM user_sessions WHERE expires_at <= ?', [new Date().toISOString()]);
    }
}
exports.DatabaseManager = DatabaseManager;
