# 小窗口病历功能说明

## 功能概述

在现有的小窗口录音功能基础上，新增了病历显示和编辑功能。当用户完成录音后，可以点击"生成病历"按钮，在小窗口上方动态创建一个病历界面区域。

## 功能特性

### 1. 动态病历界面
- **位置**: 病历界面显示在录音界面上方，与小窗口等宽
- **布局**: 垂直排列，不影响现有录音功能
- **响应式**: 自动适应窗口大小变化

### 2. 结构化病历内容
病历界面包含以下医疗记录字段：
- **患者信息**: 基本信息（姓名、年龄、性别等）
- **主诉**: 患者主要症状和就诊原因
- **现病史**: 症状发展过程、持续时间、严重程度等
- **体格检查**: 体格检查结果
- **诊断**: 初步诊断或症状描述
- **治疗方案**: 治疗建议和方案
- **用药建议**: 药物治疗建议
- **随访建议**: 后续随访计划
- **备注**: 其他重要信息或医生备注

### 3. 在线编辑功能
- **编辑模式**: 点击"编辑"按钮进入编辑模式
- **实时编辑**: 所有字段支持实时在线编辑
- **自动保存**: 编辑内容自动保存到本地状态
- **格式保持**: 支持多行文本和格式保持

### 4. 用户交互
- **关闭按钮**: 右上角"X"按钮可关闭病历界面
- **EMR集成**: "回传EMR系统"按钮（演示功能）
- **状态指示**: 生成过程中显示加载动画和状态

## 技术实现

### 组件架构
```
MiniRecordingWindow (主组件)
├── 病历界面区域
│   └── MiniMedicalRecord (新增组件)
│       ├── 头部控制栏
│       ├── 字段编辑区域
│       └── 操作按钮
└── 录音控制区域 (现有功能)
    ├── 录音状态显示
    ├── 控制按钮
    └── 错误信息显示
```

### 核心文件
- `src/renderer/components/MiniMedicalRecord.tsx` - 新增的病历组件
- `src/renderer/components/MiniRecordingWindow.tsx` - 修改的主窗口组件

### 状态管理
```typescript
// 新增状态
const [showMedicalRecord, setShowMedicalRecord] = useState(false);
const [isGeneratingRecord, setIsGeneratingRecord] = useState(false);

// 病历数据结构
interface MedicalRecordData {
  patientInfo: string;
  chiefComplaint: string;
  presentIllness: string;
  examination: string;
  diagnosis: string;
  treatmentPlan: string;
  medications: string;
  followUp: string;
  notes: string;
}
```

## 使用流程

### 1. 录音阶段
1. 用户在小窗口中开始录音
2. 系统实时进行语音识别转录
3. 用户可以暂停、继续或停止录音

### 2. 生成病历
1. 录音完成后，点击"生成病历"按钮
2. 系统显示病历界面，开始生成过程
3. 显示加载动画和"正在生成病历..."状态
4. 2秒后完成生成，显示结构化病历内容

### 3. 编辑病历
1. 点击"编辑"按钮进入编辑模式
2. 所有字段变为可编辑的文本框
3. 用户可以修改任意字段内容
4. 点击"完成"退出编辑模式

### 4. EMR集成
1. 点击"回传EMR系统"按钮
2. 显示"发送中..."状态
3. 1.5秒后显示成功提示（演示功能）

### 5. 关闭病历
1. 点击右上角"X"按钮
2. 病历界面关闭，回到录音界面
3. 可以重新生成病历或进行新的录音

## 设计特点

### 1. 一致性设计
- 使用项目现有的设计系统和主题
- 保持与大窗口界面的视觉一致性
- 统一的颜色、字体和间距规范

### 2. 用户体验
- 平滑的动画过渡效果
- 直观的状态指示
- 响应式的交互反馈
- 清晰的信息层次结构

### 3. 功能完整性
- 不影响现有录音功能
- 独立的病历管理模块
- 完整的编辑和查看功能
- 模拟的EMR系统集成

## 扩展性

### 未来可扩展功能
1. **真实AI集成**: 集成真实的LLM服务进行病历生成
2. **模板系统**: 支持不同科室的病历模板
3. **数据持久化**: 将病历数据保存到数据库
4. **打印功能**: 支持病历打印和导出
5. **EMR集成**: 真实的EMR系统集成接口
6. **多语言支持**: 支持多语言病历生成

### 配置选项
- 病历字段的显示/隐藏配置
- 编辑权限控制
- 自动保存间隔设置
- EMR系统连接配置

## 注意事项

1. **演示功能**: 当前EMR集成为演示功能，无真实后端
2. **数据安全**: 病历数据仅存储在前端状态中
3. **性能考虑**: 大量文本编辑时注意性能优化
4. **兼容性**: 确保与现有录音功能的兼容性

## 技术细节

### CSS样式
- 使用内联样式保持组件独立性
- 响应式设计适配不同屏幕尺寸
- 平滑的过渡动画效果

### 事件处理
- 防抖处理避免频繁更新
- 错误边界处理异常情况
- 内存泄漏防护

### 数据流
```
录音转录 → 生成病历 → 显示界面 → 编辑内容 → 保存/发送
```

这个新功能为小窗口录音系统增加了完整的病历管理能力，提升了系统的实用性和专业性。
