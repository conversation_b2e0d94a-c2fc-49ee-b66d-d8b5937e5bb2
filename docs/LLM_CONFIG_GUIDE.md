# 大模型配置指南

## 📍 配置位置

在牙科听译系统中，大模型配置已经单独设置为一个专门的配置模块：

1. **启动应用程序**
2. **点击左侧导航栏的"系统设置"**
3. **选择"大模型配置"选项卡**

## 🎯 功能特点

### ✅ 支持的大模型服务商

1. **OpenAI (ChatGPT)**
   - GPT-3.5 Turbo
   - GPT-4
   - GPT-4 Turbo
   - GPT-4o

2. **<PERSON> (Anthropic)**
   - Claude 3 Haiku
   - Claude 3 Sonnet
   - Claude 3 Opus

3. **智谱AI (GLM)**
   - GLM-4
   - GLM-3 Turbo

4. **通义千问 (阿里云)**
   - Qwen Turbo
   - Qwen Plus
   - Qwen Max

5. **自定义API**
   - 支持任何兼容OpenAI格式的API

## 🔧 配置步骤

### 1. 基础配置

1. **启用/禁用开关**
   - 可以快速启用或禁用大模型功能

2. **选择服务提供商**
   - 从下拉菜单中选择你要使用的服务商

3. **输入API Key**
   - 输入对应服务商的API密钥
   - 支持密码隐藏显示

4. **设置API地址**
   - 系统会自动填入默认地址
   - 可以根据需要修改为自定义地址

5. **选择模型**
   - 根据选择的服务商，自动显示可用模型
   - 支持最新的模型版本

### 2. 高级参数配置

1. **温度值 (Temperature)**
   - 范围：0-2
   - 默认：0.7
   - 控制生成文本的随机性

2. **最大Token数**
   - 范围：1-8000
   - 默认：2000
   - 控制生成文本的最大长度

3. **系统提示词**
   - 自定义AI助手的角色和行为
   - 默认：专业医疗助手提示词

## 📋 配置示例

### OpenAI 配置示例
```
服务提供商: OpenAI (ChatGPT)
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API地址: https://api.openai.com/v1
模型: gpt-3.5-turbo
温度值: 0.7
最大Token数: 2000
系统提示词: 你是一个专业的医疗助手，请根据患者的症状描述生成结构化的病历记录。
```

### Claude 配置示例
```
服务提供商: Claude (Anthropic)
API Key: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API地址: https://api.anthropic.com
模型: claude-3-sonnet
温度值: 0.5
最大Token数: 1500
系统提示词: 你是一个专业的牙科医疗助手，请根据患者的症状描述生成详细的牙科病历记录。
```

### 智谱AI 配置示例
```
服务提供商: 智谱AI (GLM)
API Key: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API地址: https://open.bigmodel.cn/api/paas/v4
模型: glm-4
温度值: 0.8
最大Token数: 2000
系统提示词: 你是一个专业的医疗AI助手，擅长分析患者症状并生成规范的医疗记录。
```

## 🔗 连接测试

配置完成后，点击**"测试连接"**按钮：

- ✅ **连接成功**：显示绿色成功提示，可以正常使用
- ❌ **连接失败**：显示红色错误提示，请检查配置
- ⚠️ **连接警告**：显示黄色警告提示，建议重新测试

## 💾 配置保存

- 配置会自动保存到本地存储
- 下次启动应用时会自动加载配置
- 支持实时配置更新

## 🎯 使用场景

配置完成后，大模型将用于以下功能：

1. **智能病历生成**
   - 基于语音转录内容生成结构化病历
   - 自动提取症状、诊断、治疗建议

2. **医疗文本分析**
   - 分析患者描述的症状
   - 提供专业的医疗建议

3. **病历格式化**
   - 将非结构化文本转换为标准病历格式
   - 确保医疗记录的专业性和准确性

## 🔒 安全说明

- API密钥仅存储在本地，不会上传到服务器
- 支持密码隐藏显示，保护敏感信息
- 建议定期更换API密钥以确保安全

## 📞 技术支持

如果在配置过程中遇到问题：

1. 检查网络连接是否正常
2. 确认API密钥是否有效
3. 验证API地址是否正确
4. 查看控制台错误信息
5. 联系技术支持团队

---

**注意**：请确保你有有效的API密钥和足够的API调用额度，以保证大模型功能的正常使用。
