# 小窗口病历功能演示

## 功能演示流程

### 1. 初始状态 - 未开始录音
```
┌─────────────────────────────────────────────────┐
│ ⬅  许慕希    ● 未开始录音           ▶        │
└─────────────────────────────────────────────────┘
```
- 显示患者姓名"许慕希"
- 状态指示为灰色圆点
- 右侧有开始录音按钮

### 2. 录音中状态
```
┌─────────────────────────────────────────────────┐
│ ⬅  许慕希    ● 录音中 01:23      ⏸  ⏹      │
└─────────────────────────────────────────────────┘
```
- 状态指示为红色闪烁圆点
- 显示录音时长
- 提供暂停和停止按钮

### 3. 录音完成状态
```
┌─────────────────────────────────────────────────┐
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```
- 状态指示为绿色圆点
- 提供重新录音和生成病历按钮

### 4. 生成病历中
```
┌─────────────────────────────────────────────────┐
│ ● 正在生成病历...              编辑 回传EMR ×  │
│ ┌─────────────────────────────────────────────┐ │
│ │              🔄                             │ │
│ │     正在分析转录内容并生成结构化病历...        │ │
│ │                                             │ │
│ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────┤
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```
- 病历界面显示在录音界面上方
- 显示加载动画和生成提示
- 头部有控制按钮

### 5. 病历生成完成
```
┌─────────────────────────────────────────────────┐
│ ● 电子病历                    编辑 回传EMR ×  │
│ ┌─────────────────────────────────────────────┐ │
│ │ 患者信息: 许慕希，女，28岁                   │ │
│ │ 主诉: 牙痛3天                              │ │
│ │ 现病史: 患者3天前无明显诱因出现右下后牙疼痛... │ │
│ │ 体格检查: 右下第一磨牙可见深龋洞...          │ │
│ │ 诊断: 1. 右下第一磨牙急性牙髓炎              │ │
│ │      2. 深龋                              │ │
│ │ 治疗方案: 1. 开髓引流                       │ │
│ │          2. 根管治疗                       │ │
│ │ 用药建议: 1. 布洛芬 0.2g tid po            │ │
│ │ 随访建议: 3天后复诊进行根管治疗              │ │
│ │ 备注: 患者疼痛明显，已告知注意事项            │ │
│ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────┤
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```
- 显示完整的结构化病历内容
- 所有字段都有相应的医疗信息
- 保持录音界面在下方

### 6. 编辑模式
```
┌─────────────────────────────────────────────────┐
│ ● 电子病历                    完成 回传EMR ×  │
│ ┌─────────────────────────────────────────────┐ │
│ │ 患者信息: [许慕希，女，28岁            ]     │ │
│ │ 主诉: [牙痛3天                        ]     │ │
│ │ 现病史: [患者3天前无明显诱因出现右下后牙疼痛  │ │
│ │        呈阵发性跳痛，夜间疼痛加重...    ]     │ │
│ │ 体格检查: [右下第一磨牙可见深龋洞，探痛明显  │ │
│ │          叩痛阳性，牙龈无红肿。        ]     │ │
│ │ 诊断: [1. 右下第一磨牙急性牙髓炎            │ │
│ │       2. 深龋                        ]     │ │
│ │ ...                                        │ │
│ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────┤
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```
- 所有字段变为可编辑的文本框
- 支持多行文本输入
- "编辑"按钮变为"完成"

### 7. EMR发送中
```
┌─────────────────────────────────────────────────┐
│ ● 电子病历                  编辑 发送中... ×  │
│ ┌─────────────────────────────────────────────┐ │
│ │ 患者信息: 许慕希，女，28岁                   │ │
│ │ 主诉: 牙痛3天                              │ │
│ │ ...                                        │ │
│ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────┤
│ ⬅  许慕希    ● 录音完成         ▶  生成      │
└─────────────────────────────────────────────────┘
```
- "回传EMR"按钮变为"发送中..."
- 按钮变为灰色不可点击状态

## 交互说明

### 按钮功能
- **⬅ 返回大窗口**: 隐藏小窗口，显示主应用
- **▶ 开始/继续录音**: 开始新录音或继续暂停的录音
- **⏸ 暂停录音**: 暂停当前录音
- **⏹ 停止录音**: 停止录音并保存
- **生成**: 基于转录内容生成病历
- **编辑/完成**: 切换编辑模式
- **回传EMR**: 发送病历到EMR系统（演示）
- **× 关闭**: 关闭病历界面

### 状态指示
- **灰色圆点**: 未开始录音
- **红色闪烁**: 正在录音
- **黄色圆点**: 录音暂停
- **绿色圆点**: 录音完成
- **黄色圆点**: 正在生成病历
- **绿色圆点**: 病历生成完成

### 数据示例
```json
{
  "patientInfo": "许慕希，女，28岁",
  "chiefComplaint": "牙痛3天",
  "presentIllness": "患者3天前无明显诱因出现右下后牙疼痛，呈阵发性跳痛，夜间疼痛加重，影响睡眠。无发热，无面部肿胀。",
  "examination": "右下第一磨牙可见深龋洞，探痛明显，叩痛阳性，牙龈无红肿。",
  "diagnosis": "1. 右下第一磨牙急性牙髓炎\n2. 深龋",
  "treatmentPlan": "1. 开髓引流\n2. 根管治疗\n3. 冠修复",
  "medications": "1. 布洛芬 0.2g tid po\n2. 甲硝唑 0.4g bid po",
  "followUp": "3天后复诊进行根管治疗",
  "notes": "患者疼痛明显，已告知注意事项"
}
```

## 技术特点

### 响应式设计
- 病历界面自动适应窗口宽度
- 文本框高度根据内容自动调整
- 滚动条在内容超出时自动显示

### 动画效果
- 病历界面平滑展开/收起
- 加载动画旋转效果
- 按钮悬停状态变化
- 状态指示闪烁动画

### 用户体验
- 清晰的视觉层次
- 直观的操作流程
- 即时的状态反馈
- 一致的设计语言

## 使用建议

### 最佳实践
1. 录音前确认患者信息
2. 录音时保持环境安静
3. 生成病历后仔细检查内容
4. 编辑时注意医疗术语准确性
5. 发送前再次确认信息完整性

### 注意事项
1. 当前为演示版本，EMR集成为模拟功能
2. 病历数据仅存储在前端，刷新后丢失
3. 建议在真实环境中添加数据持久化
4. 需要集成真实的AI服务进行病历生成

这个功能演示展示了完整的医疗录音到病历生成的工作流程，为医疗工作者提供了高效便捷的病历记录工具。
