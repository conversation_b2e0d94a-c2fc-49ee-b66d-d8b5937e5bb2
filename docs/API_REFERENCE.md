# API参考文档 - 诊间听译系统

## 📋 目录

1. [概述](#概述)
2. [内部API (Electron IPC)](#内部api-electron-ipc)
3. [外部API集成](#外部api集成)
4. [数据模型](#数据模型)
5. [错误处理](#错误处理)
6. [使用示例](#使用示例)

## 🔍 概述

诊间听译系统采用Electron架构，通过IPC通信实现前后端数据交互，并集成多种云服务API提供语音识别和AI病历生成功能。

### 架构图
```
渲染进程 (React) ←→ 主进程 (Electron) ←→ 外部云服务
        ↓                    ↓
    本地存储              SQLite数据库
```

## 🔗 内部API (Electron IPC)

### 认证API

#### `auth:login`
用户登录认证

**请求参数:**
```typescript
{
  username: string;
  password: string;
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    token: string;
    user: UserInfo;
  };
  error?: string;
}
```

**使用示例:**
```typescript
const result = await window.electronAPI.auth.login('admin', '123456');
if (result.success) {
  console.log('登录成功:', result.data.user);
}
```

#### `auth:logout`
用户登出

**请求参数:** 无

**响应格式:**
```typescript
{
  success: boolean;
  error?: string;
}
```

#### `auth:getCurrentUser`
获取当前登录用户信息

**请求参数:** 无

**响应格式:**
```typescript
{
  success: boolean;
  data?: UserInfo;
  error?: string;
}
```

### 录音API

#### `recording:start`
开始录音

**请求参数:**
```typescript
{
  options?: {
    format?: 'wav' | 'mp3';
    sampleRate?: number;
    channels?: number;
    maxDuration?: number;
  };
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    recordingId: string;
    startTime: Date;
  };
  error?: string;
}
```

#### `recording:stop`
停止录音

**请求参数:**
```typescript
{
  recordingId: string;
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    filePath: string;
    duration: number;
    size: number;
  };
  error?: string;
}
```

#### `recording:list`
获取录音列表

**请求参数:**
```typescript
{
  page?: number;
  limit?: number;
  filters?: {
    startDate?: Date;
    endDate?: Date;
    status?: string;
  };
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    recordings: Recording[];
    total: number;
    page: number;
    limit: number;
  };
  error?: string;
}
```

#### `recording:delete`
删除录音

**请求参数:**
```typescript
{
  recordingId: string;
}
```

**响应格式:**
```typescript
{
  success: boolean;
  error?: string;
}
```

### 云服务API

#### `cloud:transcribe`
音频转录

**请求参数:**
```typescript
{
  audioPath: string;
  options?: {
    provider?: 'baidu' | 'aliyun' | 'web';
    language?: string;
    enablePunctuation?: boolean;
  };
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    text: string;
    confidence: number;
    duration: number;
  };
  error?: string;
}
```

#### `cloud:generateMedicalRecord`
生成病历

**请求参数:**
```typescript
{
  transcriptionText: string;
  options?: {
    provider?: 'openai' | 'claude' | 'zhipu' | 'qwen';
    template?: string;
    maxTokens?: number;
    temperature?: number;
  };
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: MedicalRecord;
  error?: string;
}
```

#### `cloud:testConnection`
测试云服务连接

**请求参数:** 无

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    asr: boolean;
    llm: boolean;
    details: {
      asrProvider: string;
      llmProvider: string;
      latency: number;
    };
  };
  error?: string;
}
```

### 数据管理API

#### `data:getMedicalRecords`
获取病历列表

**请求参数:**
```typescript
{
  page?: number;
  limit?: number;
  filters?: {
    patientName?: string;
    startDate?: Date;
    endDate?: Date;
  };
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    records: MedicalRecord[];
    total: number;
    page: number;
    limit: number;
  };
  error?: string;
}
```

#### `data:saveMedicalRecord`
保存病历

**请求参数:**
```typescript
{
  record: MedicalRecord;
}
```

**响应格式:**
```typescript
{
  success: boolean;
  data?: {
    recordId: string;
  };
  error?: string;
}
```

#### `data:deleteMedicalRecord`
删除病历

**请求参数:**
```typescript
{
  recordId: string;
}
```

**响应格式:**
```typescript
{
  success: boolean;
  error?: string;
}
```

### 配置管理API

#### `config:get`
获取系统配置

**请求参数:** 无

**响应格式:**
```typescript
{
  success: boolean;
  data?: AppSettings;
  error?: string;
}
```

#### `config:update`
更新系统配置

**请求参数:**
```typescript
{
  settings: Partial<AppSettings>;
}
```

**响应格式:**
```typescript
{
  success: boolean;
  error?: string;
}
```

## ☁️ 外部API集成

### 百度语音识别API

#### 接口地址
```
POST https://vop.baidu.com/server_api
```

#### 请求参数
```json
{
  "format": "wav",
  "rate": 16000,
  "channel": 1,
  "cuid": "unique_device_id",
  "token": "access_token",
  "speech": "base64_encoded_audio",
  "len": 1024
}
```

#### 响应格式
```json
{
  "err_no": 0,
  "err_msg": "success.",
  "corpus_no": "15984125",
  "sn": "481D633F-73BA-726F-49EF-8659ACCC2F3D",
  "result": ["识别结果文本"]
}
```

### OpenAI Chat Completions API

#### 接口地址
```
POST https://api.openai.com/v1/chat/completions
```

#### 请求参数
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的医疗助手，请根据患者的症状描述生成结构化的病历记录。"
    },
    {
      "role": "user",
      "content": "患者主诉：牙痛三天，夜间加重..."
    }
  ],
  "temperature": 0.7,
  "max_tokens": 2000
}
```

#### 响应格式
```json
{
  "id": "chatcmpl-xxx",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "## 病历记录\n\n**患者信息**\n- 主诉：牙痛三天..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 150,
    "completion_tokens": 800,
    "total_tokens": 950
  }
}
```

## 📊 数据模型

### UserInfo
```typescript
interface UserInfo {
  id: string;
  username: string;
  email?: string;
  role: 'admin' | 'doctor' | 'nurse';
  createdAt: Date;
  lastLoginAt?: Date;
  isActive: boolean;
}
```

### Recording
```typescript
interface Recording {
  id: string;
  filename: string;
  originalName?: string;
  duration: number; // 秒
  size: number; // 字节
  format: 'wav' | 'mp3';
  sampleRate: number;
  channels: number;
  createdAt: Date;
  updatedAt: Date;
  transcriptionStatus: 'pending' | 'processing' | 'completed' | 'failed';
  transcriptionText?: string;
  transcriptionConfidence?: number;
  userId: string;
}
```

### MedicalRecord
```typescript
interface MedicalRecord {
  id: string;
  // 患者信息
  patientName?: string;
  patientAge?: number;
  patientGender?: 'male' | 'female' | 'other';
  patientId?: string;

  // 病历内容
  chiefComplaint?: string; // 主诉
  presentIllness?: string; // 现病史
  pastHistory?: string; // 既往史
  examination?: string; // 体格检查
  auxiliaryExamination?: string; // 辅助检查
  diagnosis?: string; // 诊断
  treatment?: string; // 治疗方案
  medications?: string; // 用药
  followUp?: string; // 随访
  notes?: string; // 备注

  // 元数据
  createdAt: Date;
  updatedAt: Date;
  recordingId?: string;
  userId: string;
  status: 'draft' | 'completed' | 'archived';
  version: number;
}
```

### AppSettings
```typescript
interface AppSettings {
  // 语音识别设置
  speechRecognition: {
    provider: 'web' | 'baidu' | 'aliyun';
    apiKey: string;
    language: string;
    sampleRate: number;
    enableContinuous: boolean;
    enableInterimResults: boolean;
  };

  // 录音设置
  recording: {
    autoSave: boolean;
    saveFormat: 'wav' | 'mp3';
    quality: 'high' | 'medium' | 'low';
    maxDuration: number; // 分钟
  };

  // LLM设置
  llm: {
    enabled: boolean;
    provider: 'openai' | 'claude' | 'zhipu' | 'qwen' | 'custom';
    apiKey: string;
    apiUrl: string;
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
  };

  // UI设置
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: 'zh-CN' | 'en-US';
    showNotifications: boolean;
    autoStartRecording: boolean;
  };

  // 数据设置
  data: {
    autoBackup: boolean;
    backupInterval: number; // 小时
    retentionDays: number;
    exportFormat: 'json' | 'csv' | 'pdf';
  };
}
```

## ⚠️ 错误处理

### 错误码定义

| 错误码 | 类别 | 描述 | 处理建议 |
|--------|------|------|----------|
| 1001 | 认证 | 用户名或密码错误 | 检查登录凭据 |
| 1002 | 认证 | 会话已过期 | 重新登录 |
| 1003 | 认证 | 权限不足 | 联系管理员 |
| 2001 | 录音 | 麦克风权限被拒绝 | 授权麦克风权限 |
| 2002 | 录音 | 录音设备不可用 | 检查音频设备 |
| 2003 | 录音 | 录音文件损坏 | 重新录音 |
| 2004 | 录音 | 存储空间不足 | 清理磁盘空间 |
| 3001 | 网络 | 网络连接失败 | 检查网络连接 |
| 3002 | 网络 | API密钥无效 | 检查API配置 |
| 3003 | 网络 | 服务配额不足 | 联系服务提供商 |
| 3004 | 网络 | 请求超时 | 重试或检查网络 |
| 4001 | 数据 | 数据库连接失败 | 检查数据库状态 |
| 4002 | 数据 | 数据格式错误 | 验证数据格式 |
| 4003 | 数据 | 记录不存在 | 检查记录ID |
| 5001 | 系统 | 文件系统错误 | 检查文件权限 |
| 5002 | 系统 | 内存不足 | 关闭其他应用 |
| 5003 | 系统 | 配置文件损坏 | 重置配置 |

### 错误响应格式
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: number;
    message: string;
    details?: string;
    timestamp: Date;
    requestId?: string;
  };
}
```

## 💡 使用示例

### 完整的录音转录流程
```typescript
// 1. 开始录音
const startResult = await window.electronAPI.recording.start({
  format: 'wav',
  sampleRate: 16000,
  channels: 1
});

if (!startResult.success) {
  console.error('录音启动失败:', startResult.error);
  return;
}

// 2. 停止录音
const stopResult = await window.electronAPI.recording.stop({
  recordingId: startResult.data.recordingId
});

if (!stopResult.success) {
  console.error('录音停止失败:', stopResult.error);
  return;
}

// 3. 转录音频
const transcribeResult = await window.electronAPI.cloud.transcribe({
  audioPath: stopResult.data.filePath,
  options: {
    provider: 'baidu',
    language: 'zh-CN'
  }
});

if (!transcribeResult.success) {
  console.error('转录失败:', transcribeResult.error);
  return;
}

// 4. 生成病历
const recordResult = await window.electronAPI.cloud.generateMedicalRecord({
  transcriptionText: transcribeResult.data.text,
  options: {
    provider: 'openai',
    temperature: 0.7
  }
});

if (!recordResult.success) {
  console.error('病历生成失败:', recordResult.error);
  return;
}

// 5. 保存病历
const saveResult = await window.electronAPI.data.saveMedicalRecord({
  record: recordResult.data
});

if (saveResult.success) {
  console.log('病历保存成功:', saveResult.data.recordId);
}
```

### 错误处理示例
```typescript
try {
  const result = await window.electronAPI.recording.start();
  if (!result.success) {
    switch (result.error.code) {
      case 2001:
        showPermissionDialog();
        break;
      case 2002:
        showDeviceErrorDialog();
        break;
      default:
        showGenericErrorDialog(result.error.message);
    }
  }
} catch (error) {
  console.error('Unexpected error:', error);
  showGenericErrorDialog('系统错误，请重试');
}
```

---

*本API文档基于诊间听译 v1.0.0 版本，如有更新请参考最新版本文档。*
