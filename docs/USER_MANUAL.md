# 诊间听译 - 用户使用手册

## 📖 目录

1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [功能使用](#功能使用)
4. [最新更新](#最新更新)
5. [设置配置](#设置配置)
6. [常见问题](#常见问题)
7. [故障排除](#故障排除)

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **内存**: 4GB RAM 以上
- **存储**: 500MB 可用空间
- **网络**: 稳定的互联网连接（用于云服务）
- **音频**: 麦克风设备

### 安装步骤

1. **下载应用**
   - 从官方网站下载对应系统的安装包
   - Windows: `诊间听译-Setup-x.x.x.exe`
   - macOS: `诊间听译-x.x.x.dmg`
   - Linux: `诊间听译-x.x.x.AppImage`

2. **安装应用**
   - Windows: 双击exe文件，按提示安装
   - macOS: 打开dmg文件，拖拽到Applications文件夹
   - Linux: 给AppImage文件添加执行权限后运行

3. **首次启动**
   - 启动应用后会看到登录界面
   - 使用默认账号登录：
     - 用户名: `admin`
     - 密码: `123456`

## 🖥️ 界面介绍

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ 诊间听译                                    ○ ○ ○        │
├─────────────────────────────────────────────────────────┤
│ 侧边栏          │              主内容区域                │
│                │                                       │
│ 🏠 仪表板       │                                       │
│ 🎙️ 语音录制     │                                       │
│ 📁 录音管理     │                                       │
│ 📋 病历管理     │                                       │
│ ⚙️ 系统设置     │                                       │
│                │                                       │
└─────────────────────────────────────────────────────────┘
```

### 侧边栏导航

- **🏠 仪表板**: 系统概览和快速操作
- **🎙️ 语音录制**: 录音控制和实时转录
- **📁 录音管理**: 录音文件管理和播放
- **📋 病历管理**: 病历生成和管理
- **⚙️ 系统设置**: 系统配置和参数设置

## 🎯 功能使用

### 1. 语音录制

#### 开始录音
1. 点击侧边栏的"语音录制"
2. 确保麦克风权限已授权
3. 点击"开始录音"按钮
4. 开始说话，系统会实时显示录音状态

#### 录音控制
- **暂停/继续**: 点击暂停按钮可暂停录音，再次点击继续
- **停止录音**: 点击停止按钮结束录音
- **录音时长**: 实时显示当前录音时长（不包括暂停时间）

#### 实时转录
- 录音过程中会显示实时语音识别结果
- 支持中文普通话识别
- 识别结果会实时更新

### 2. 录音管理

#### 查看录音列表
1. 点击"录音管理"进入录音列表
2. 显示所有历史录音文件
3. 包含录音时间、时长、转录状态等信息

#### 录音操作
- **播放**: 点击播放按钮收听录音
- **下载**: 下载录音文件到本地
- **删除**: 删除不需要的录音文件
- **生成病历**: 基于录音内容生成病历

### 3. 病历生成

#### 自动生成病历
1. 在录音管理中选择录音文件
2. 点击"生成病历"按钮
3. 系统会自动进行语音转录和病历生成
4. 生成完成后可在病历管理中查看

#### 病历内容结构
- **患者信息**: 姓名、年龄、性别等
- **主诉**: 患者主要症状描述
- **现病史**: 疾病发展过程
- **体格检查**: 检查结果记录
- **诊断**: 初步诊断结果
- **治疗方案**: 建议的治疗措施
- **用药建议**: 药物使用指导
- **复诊安排**: 后续随访计划

### 4. 病历管理

#### 查看病历列表
1. 点击"病历管理"查看所有病历
2. 支持按时间、患者姓名等筛选
3. 显示病历创建时间和状态

#### 病历操作
- **查看详情**: 点击病历查看完整内容
- **编辑修改**: 对生成的病历进行编辑
- **导出**: 导出为PDF或Word格式
- **删除**: 删除不需要的病历记录

## 🆕 最新更新

### 2025年8月10日更新

#### 🎯 仪表板优化
- **界面简化**: 移除了右上角的绿色提示按钮，界面更加简洁
- **布局优化**: 4个指标卡现在固定在一排显示，不会因屏幕大小而换行
- **性能提升**: 移除了每30秒的自动刷新，减少不必要的网络请求
- **标题更新**: 页面标题更改为"诊间听译数据概览"，更准确反映功能

#### 🗂️ 录音管理增强
- **删除确认优化**: 使用现代化的确认对话框替代原生弹窗
  - 显示具体的录音文件名
  - 红色警告图标提示危险操作
  - 优雅的动画效果和模糊背景
- **操作反馈**: 删除成功/失败会显示相应的消息提示
- **安全性提升**: 更明确的确认步骤，防止误删操作

#### 🎙️ 小窗口录制功能优化
- **录制稳定性**: 完全重构录制逻辑，与主窗口保持一致
  - 更好的服务状态管理
  - 完善的错误处理机制
  - 优化的暂停/恢复功能
- **界面简化**: 移除录音完成后的复杂按钮栏
  - 不再显示返回大窗口、重新录音等按钮
  - 保持小窗口简洁的设计理念
- **滚动修复**: 修复了病历内容无法滚动的问题
  - 优化滚动条样式
  - 解决拖拽区域冲突
  - 确保内容可以正常浏览

#### 💡 使用建议
1. **仪表板**: 现在页面加载更快，指标卡布局更稳定
2. **录音管理**: 删除录音时请仔细确认文件名，避免误删
3. **小窗口录制**: 录音完成后界面会自动回到初始状态，可继续进行新的录音

## ⚙️ 设置配置

### 大模型配置

1. 进入"系统设置" → "大模型配置"
2. 配置项说明：
   - **启用大模型服务**: 开启/关闭AI病历生成功能
   - **服务提供商**: 选择AI服务商（OpenAI、Claude等）
   - **API Key**: 输入对应服务商的API密钥
   - **API地址**: 配置API服务地址
   - **模型**: 选择使用的AI模型
   - **温度**: 控制生成内容的随机性（0-1）
   - **最大Token数**: 限制生成内容的长度
   - **系统提示词**: 自定义AI生成病历的指导语

### 语音识别设置

1. 进入"系统设置" → "语音识别设置"
2. 配置项说明：
   - **识别服务**: 选择语音识别服务商
   - **API Key**: 输入语音识别服务的API密钥
   - **识别语言**: 设置识别语言（默认中文）
   - **采样率**: 音频采样率设置
   - **连续识别**: 是否启用连续语音识别
   - **临时结果**: 是否显示识别中间结果

### 录音设置

1. 进入"系统设置" → "录音设置"
2. 配置项说明：
   - **自动保存**: 录音结束后自动保存
   - **保存格式**: 选择音频文件格式（WAV/MP3）
   - **录音质量**: 设置音频质量（高/中/低）
   - **最大时长**: 单次录音最大时长限制

## ❓ 常见问题

### Q1: 无法开始录音怎么办？
**A**: 请检查以下几点：
1. 确认麦克风设备正常工作
2. 检查浏览器/应用的麦克风权限
3. 确认没有其他应用占用麦克风
4. 重启应用后重试

### Q2: 语音识别不准确怎么办？
**A**: 可以尝试：
1. 确保录音环境安静
2. 说话清晰，语速适中
3. 检查麦克风距离和音量
4. 在设置中调整识别参数

### Q3: 病历生成失败怎么办？
**A**: 请检查：
1. 网络连接是否正常
2. API Key是否正确配置
3. 账户余额是否充足
4. 录音内容是否清晰完整

### Q4: 如何备份数据？
**A**: 应用数据存储在本地：
- Windows: `%APPDATA%/诊间听译/`
- macOS: `~/Library/Application Support/诊间听译/`
- Linux: `~/.config/诊间听译/`

定期备份该目录即可保存所有数据。

## 🔧 故障排除

### 应用无法启动
1. 检查系统兼容性
2. 以管理员权限运行
3. 重新安装应用
4. 查看错误日志

### 网络连接问题
1. 检查网络连接
2. 确认防火墙设置
3. 尝试更换网络环境
4. 联系网络管理员

### 性能问题
1. 关闭不必要的后台程序
2. 确保有足够的内存空间
3. 清理临时文件
4. 重启应用

## 📞 技术支持

如果遇到其他问题，请联系技术支持：

- **邮箱**: <EMAIL>
- **电话**: 400-xxx-xxxx
- **在线客服**: 工作日 9:00-18:00

---

*本手册基于诊间听译 v1.0.0 版本编写，如有更新请以最新版本为准。*
