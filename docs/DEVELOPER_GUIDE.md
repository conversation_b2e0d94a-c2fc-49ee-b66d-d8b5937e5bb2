# 开发者指南 - 诊间听译系统

## 📋 目录

1. [开发环境搭建](#开发环境搭建)
2. [项目结构](#项目结构)
3. [核心技术栈](#核心技术栈)
4. [开发规范](#开发规范)
5. [调试指南](#调试指南)
6. [测试指南](#测试指南)
7. [贡献指南](#贡献指南)

## 🛠️ 开发环境搭建

### 系统要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **Python**: >= 3.8.0 (用于native模块编译)
- **Git**: 最新版本

### 环境安装

#### 1. 克隆项目
```bash
git clone <repository-url>
cd dental-transcription-client
```

#### 2. 安装依赖
```bash
# 安装项目依赖
npm install

# 如果遇到问题，清理缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 3. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

环境变量配置：
```env
# 开发模式
NODE_ENV=development

# 数据库配置
DB_PATH=./data/app.db

# 日志配置
LOG_LEVEL=debug
LOG_PATH=./logs

# API配置（可选，用于测试）
OPENAI_API_KEY=your_openai_api_key
BAIDU_API_KEY=your_baidu_api_key
BAIDU_SECRET_KEY=your_baidu_secret_key
```

#### 4. 启动开发环境
```bash
# 启动开发服务器
npm run dev

# 或者分别启动
npm run dev:renderer  # 启动React开发服务器
npm run dev:main      # 启动Electron主进程
```

## 📁 项目结构

```
dental-transcription-client/
├── docs/                           # 项目文档
│   ├── API_REFERENCE.md           # API参考文档
│   ├── USER_MANUAL.md             # 用户手册
│   ├── DEPLOYMENT_GUIDE.md        # 部署指南
│   └── DEVELOPER_GUIDE.md         # 开发者指南
├── src/
│   ├── main/                      # Electron主进程
│   │   ├── main.ts               # 主进程入口
│   │   ├── preload.ts            # 预加载脚本
│   │   ├── database/             # 数据库相关
│   │   │   ├── database.ts       # 数据库连接
│   │   │   └── migrations/       # 数据库迁移
│   │   ├── services/             # 主进程服务
│   │   │   ├── AuthService.ts    # 认证服务
│   │   │   ├── CloudService.ts   # 云服务集成
│   │   │   └── RecordingService.ts # 录音服务
│   │   └── handlers/             # IPC处理器
│   ├── renderer/                 # React渲染进程
│   │   ├── components/           # 通用组件
│   │   │   ├── common/          # 基础组件
│   │   │   ├── forms/           # 表单组件
│   │   │   └── layout/          # 布局组件
│   │   ├── pages/               # 页面组件
│   │   │   ├── Dashboard.tsx    # 仪表板
│   │   │   ├── VoiceRecording.tsx # 语音录制
│   │   │   ├── RecordingManagement.tsx # 录音管理
│   │   │   ├── MedicalRecords.tsx # 病历管理
│   │   │   └── Settings.tsx     # 系统设置
│   │   ├── services/            # 前端服务
│   │   │   ├── SettingsService.ts # 设置服务
│   │   │   └── ApiService.ts    # API服务
│   │   ├── hooks/               # 自定义Hooks
│   │   ├── utils/               # 工具函数
│   │   ├── types/               # TypeScript类型定义
│   │   └── styles/              # 样式文件
│   └── shared/                  # 共享代码
│       ├── types/               # 共享类型定义
│       └── constants/           # 常量定义
├── tests/                       # 测试文件
│   ├── unit/                    # 单元测试
│   ├── integration/             # 集成测试
│   └── e2e/                     # 端到端测试
├── public/                      # 静态资源
├── dist/                        # 构建输出
├── release/                     # 打包输出
├── package.json                 # 项目配置
├── tsconfig.json               # TypeScript配置
├── webpack.config.js           # Webpack配置
├── electron-builder.json       # Electron Builder配置
└── README.md                   # 项目说明
```

## 🔧 核心技术栈

### 前端技术
- **Electron**: 跨平台桌面应用框架
- **React**: 用户界面库
- **TypeScript**: 类型安全的JavaScript
- **Ant Design**: UI组件库
- **Styled Components**: CSS-in-JS样式解决方案

### 后端技术
- **Node.js**: JavaScript运行时
- **SQLite**: 轻量级数据库
- **bcryptjs**: 密码加密
- **crypto-js**: 加密工具库

### 开发工具
- **Webpack**: 模块打包器
- **Babel**: JavaScript编译器
- **ESLint**: 代码检查工具
- **Prettier**: 代码格式化工具
- **Vitest**: 测试框架

### 云服务集成
- **百度语音识别**: ASR服务
- **OpenAI GPT**: LLM服务
- **Claude**: AI助手服务
- **智谱AI**: 国产LLM服务

## 📝 开发规范

### 代码规范

#### TypeScript规范
```typescript
// 使用接口定义类型
interface UserInfo {
  id: string;
  username: string;
  email?: string;
}

// 使用枚举定义常量
enum RecordingStatus {
  IDLE = 'idle',
  RECORDING = 'recording',
  PAUSED = 'paused'
}

// 使用泛型提高代码复用性
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
```

#### React组件规范
```typescript
// 使用函数组件和Hooks
import React, { useState, useEffect } from 'react';

interface Props {
  title: string;
  onSave: (data: any) => void;
}

const MyComponent: React.FC<Props> = ({ title, onSave }) => {
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    // 副作用逻辑
  }, []);

  return (
    <div>
      <h1>{title}</h1>
      {/* 组件内容 */}
    </div>
  );
};

export default MyComponent;
```

#### 文件命名规范
- **组件文件**: PascalCase (如 `VoiceRecorder.tsx`)
- **工具文件**: camelCase (如 `audioUtils.ts`)
- **常量文件**: UPPER_SNAKE_CASE (如 `API_CONSTANTS.ts`)
- **类型文件**: camelCase (如 `userTypes.ts`)

### Git提交规范

使用Conventional Commits规范：

```bash
# 功能开发
git commit -m "feat: 添加语音录制功能"

# 问题修复
git commit -m "fix: 修复录音停止时的内存泄漏问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 样式调整
git commit -m "style: 调整录音界面布局"

# 重构代码
git commit -m "refactor: 重构设置管理模块"

# 性能优化
git commit -m "perf: 优化音频处理性能"

# 测试相关
git commit -m "test: 添加录音功能单元测试"
```

### 分支管理规范

```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/voice-recording    # 语音录制功能
feature/medical-records   # 病历管理功能

# 修复分支
hotfix/recording-bug      # 紧急修复录音问题

# 发布分支
release/v1.0.0           # 版本发布准备
```

## 🐛 调试指南

### 主进程调试

#### 1. 使用VSCode调试
创建 `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Main Process",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}",
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron",
      "windows": {
        "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"
      },
      "args": ["--inspect=5858", "."],
      "outputCapture": "std"
    }
  ]
}
```

#### 2. 日志调试
```typescript
// 在主进程中添加日志
import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

const logPath = path.join(app.getPath('userData'), 'logs');
if (!fs.existsSync(logPath)) {
  fs.mkdirSync(logPath, { recursive: true });
}

const log = (message: string) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  fs.appendFileSync(path.join(logPath, 'main.log'), logMessage);
  console.log(logMessage);
};

// 使用日志
log('应用启动');
```

### 渲染进程调试

#### 1. 开发者工具
```typescript
// 在主进程中打开开发者工具
if (isDev) {
  mainWindow.webContents.openDevTools();
}
```

#### 2. React DevTools
```bash
# 安装React DevTools
npm install -g react-devtools

# 启动React DevTools
react-devtools
```

### 网络请求调试

```typescript
// 添加请求拦截器
import axios from 'axios';

axios.interceptors.request.use(
  (config) => {
    console.log('Request:', config);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    console.log('Response:', response);
    return response;
  },
  (error) => {
    console.error('Response Error:', error);
    return Promise.reject(error);
  }
);
```

## 🧪 测试指南

### 单元测试

#### 1. 组件测试
```typescript
// tests/unit/VoiceRecorder.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import VoiceRecorder from '../../src/renderer/components/VoiceRecorder';

describe('VoiceRecorder', () => {
  test('应该渲染录音按钮', () => {
    render(<VoiceRecorder />);
    const recordButton = screen.getByText('开始录音');
    expect(recordButton).toBeInTheDocument();
  });

  test('点击录音按钮应该开始录音', () => {
    const mockOnStart = jest.fn();
    render(<VoiceRecorder onStart={mockOnStart} />);
    
    const recordButton = screen.getByText('开始录音');
    fireEvent.click(recordButton);
    
    expect(mockOnStart).toHaveBeenCalled();
  });
});
```

#### 2. 服务测试
```typescript
// tests/unit/AuthService.test.ts
import { AuthService } from '../../src/main/services/AuthService';

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    authService = new AuthService();
  });

  test('应该能够验证正确的用户凭据', async () => {
    const result = await authService.login('admin', '123456');
    expect(result.success).toBe(true);
    expect(result.user).toBeDefined();
  });

  test('应该拒绝错误的用户凭据', async () => {
    const result = await authService.login('admin', 'wrong-password');
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });
});
```

### 集成测试

```typescript
// tests/integration/recording-flow.test.ts
import { app } from 'electron';
import { RecordingService } from '../../src/main/services/RecordingService';
import { CloudService } from '../../src/main/services/CloudService';

describe('录音转录流程', () => {
  let recordingService: RecordingService;
  let cloudService: CloudService;

  beforeAll(async () => {
    await app.whenReady();
    recordingService = new RecordingService();
    cloudService = new CloudService();
  });

  test('完整的录音转录流程', async () => {
    // 1. 开始录音
    const startResult = await recordingService.startRecording();
    expect(startResult.success).toBe(true);

    // 2. 模拟录音时间
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 3. 停止录音
    const stopResult = await recordingService.stopRecording();
    expect(stopResult.success).toBe(true);
    expect(stopResult.filePath).toBeDefined();

    // 4. 转录音频
    const transcribeResult = await cloudService.transcribe(stopResult.filePath);
    expect(transcribeResult.success).toBe(true);
    expect(transcribeResult.text).toBeDefined();
  });
});
```

### 端到端测试

```typescript
// tests/e2e/app.test.ts
import { Application } from 'spectron';
import * as path from 'path';

describe('应用端到端测试', () => {
  let app: Application;

  beforeEach(async () => {
    app = new Application({
      path: path.join(__dirname, '../../node_modules/.bin/electron'),
      args: [path.join(__dirname, '../../dist/main/main.js')]
    });
    await app.start();
  });

  afterEach(async () => {
    if (app && app.isRunning()) {
      await app.stop();
    }
  });

  test('应该启动应用并显示登录界面', async () => {
    const windowCount = await app.client.getWindowCount();
    expect(windowCount).toBe(1);

    const title = await app.client.getTitle();
    expect(title).toBe('诊间听译');

    const loginForm = await app.client.$('#login-form');
    expect(await loginForm.isDisplayed()).toBe(true);
  });

  test('应该能够登录并进入主界面', async () => {
    // 输入用户名密码
    await app.client.$('#username').setValue('admin');
    await app.client.$('#password').setValue('123456');
    
    // 点击登录按钮
    await app.client.$('#login-button').click();
    
    // 等待主界面加载
    await app.client.waitUntil(async () => {
      const dashboard = await app.client.$('#dashboard');
      return await dashboard.isDisplayed();
    });

    // 验证主界面元素
    const sidebar = await app.client.$('#sidebar');
    expect(await sidebar.isDisplayed()).toBe(true);
  });
});
```

### 运行测试

```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 🆕 最新更新技术细节

### 2025年8月10日更新

#### 仪表板模块优化 (`DentalClinicDashboard.tsx`)

**布局系统改进**
```typescript
// 修改前：响应式布局
gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))'

// 修改后：固定4列布局
gridTemplateColumns: 'repeat(4, 1fr)'
```

**数据刷新机制优化**
```typescript
// 移除定时刷新逻辑
useEffect(() => {
  loadDashboardData();
  // 删除了以下代码：
  // const unsubscribe = dashboardService.subscribe(loadDashboardData);
  // const stopRealTimeUpdates = dashboardService.startRealTimeUpdates(30000);
}, []);
```

#### 录音管理模块增强 (`RecordingList.tsx`)

**现代化确认对话框**
```typescript
// 新增状态管理
const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
const [recordingToDelete, setRecordingToDelete] = useState<RecordingFile | null>(null);

// 替换原生confirm()
const showDeleteConfirm = (recording: RecordingFile) => {
  setRecordingToDelete(recording);
  setDeleteConfirmVisible(true);
};

// 集成全局消息系统
const { success: showSuccessMessage, error: showErrorMessage } = useGlobalMessage();
```

**ConfirmDialog组件集成**
```typescript
<ConfirmDialog
  visible={deleteConfirmVisible}
  title="确认删除录音"
  content={`确定要删除录音文件"${recordingToDelete?.filename || ''}"吗？此操作不可撤销。`}
  confirmText="确认删除"
  cancelText="取消"
  confirmButtonType="danger"
  onConfirm={handleDeleteConfirm}
  onCancel={handleDeleteCancel}
  icon={<ExclamationCircleOutlined style={{ fontSize: '20px', color: '#EF4444' }} />}
/>
```

#### 小窗口录制功能重构 (`MiniRecordingWindow.tsx`)

**服务状态管理增强**
```typescript
// 新增状态
const [isInitializing, setIsInitializing] = useState(false);
const [serviceStatus, setServiceStatus] = useState<'idle' | 'initializing' | 'ready' | 'error'>('idle');

// 优化初始化流程
const initializeSpeechService = async () => {
  try {
    setIsInitializing(true);
    setServiceStatus('initializing');
    setError(null);

    // 创建和配置服务...

    setServiceStatus('ready');
  } catch (error) {
    setServiceStatus('error');
    setError(`语音识别服务初始化失败: ${(error as Error).message}`);
  } finally {
    setIsInitializing(false);
  }
};
```

### 技术架构改进

#### 错误处理标准化
- 所有异步操作都使用 try-catch 包装
- 错误信息包含具体的错误描述
- 添加了资源清理确保机制

#### 状态管理优化
- 统一的状态管理模式
- 更好的状态同步机制
- 完善的状态重置逻辑

#### 用户体验提升
- 现代化的UI组件设计
- 一致的交互体验
- 更好的视觉反馈

### 开发注意事项

1. **状态管理**: 新增的状态变量需要正确初始化和清理
2. **错误处理**: 所有异步操作都应该有适当的错误处理
3. **资源清理**: 确保在组件卸载时清理所有资源
4. **用户反馈**: 使用全局消息系统提供操作反馈

## 🤝 贡献指南

### 开发流程

1. **Fork项目**
   ```bash
   # 在GitHub上Fork项目到你的账户
   git clone https://github.com/your-username/dental-transcription-client.git
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **开发功能**
   - 遵循代码规范
   - 添加必要的测试
   - 更新相关文档

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   git push origin feature/your-feature-name
   ```

5. **创建Pull Request**
   - 在GitHub上创建PR
   - 填写详细的PR描述
   - 等待代码审查

### 代码审查清单

- [ ] 代码符合项目规范
- [ ] 添加了必要的测试
- [ ] 测试全部通过
- [ ] 更新了相关文档
- [ ] 没有引入安全漏洞
- [ ] 性能没有明显下降

### 问题报告

使用GitHub Issues报告问题时，请包含：

1. **问题描述**: 清晰描述遇到的问题
2. **复现步骤**: 详细的复现步骤
3. **期望行为**: 期望的正确行为
4. **实际行为**: 实际发生的行为
5. **环境信息**: 操作系统、Node.js版本等
6. **错误日志**: 相关的错误信息和日志

---

*本开发者指南基于诊间听译 v1.0.0 版本，如有更新请参考最新版本文档。*
