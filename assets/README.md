# 应用图标说明

## 图标文件

本目录包含了诊间听译应用的所有图标文件：

### 源文件
- `app-icon.svg` - 原始SVG矢量图标，包含医疗十字、麦克风和文档元素

### PNG 图标（多种尺寸）
- `icon-16.png` - 16x16 像素
- `icon-32.png` - 32x32 像素  
- `icon-64.png` - 64x64 像素
- `icon-128.png` - 128x128 像素
- `icon-256.png` - 256x256 像素
- `icon-512.png` - 512x512 像素
- `icon-1024.png` - 1024x1024 像素
- `icon.png` - 通用图标（256x256）

### 平台特定图标
- `icon.icns` - macOS 应用图标
- `icon.ico` - Windows 应用图标
- `icon.iconset/` - macOS iconset 目录（用于生成 .icns）

## 图标设计

图标采用现代医疗主题设计：
- **背景**: 蓝色渐变 (#4FC3F7 到 #1976D2)
- **医疗十字**: 白色，象征医疗服务
- **麦克风**: 白色，象征语音录制功能
- **文档**: 白色，象征病历记录

## 使用配置

### Electron 主进程配置
在 `src/main/main.ts` 中配置：
```typescript
icon: process.platform !== 'darwin' ? path.join(__dirname, '../../assets/icon.png') : undefined
```

### Electron Builder 配置
在 `package.json` 中配置：
```json
"build": {
  "mac": {
    "icon": "assets/icon.icns"
  },
  "win": {
    "icon": "assets/icon.ico"
  },
  "linux": {
    "icon": "assets/icon.png"
  }
}
```

## 重新生成图标

如需修改图标，请：

1. 编辑 `app-icon.svg` 源文件
2. 运行以下命令重新生成所有尺寸：

```bash
# 生成 PNG 文件
rsvg-convert -w 16 -h 16 assets/app-icon.svg -o assets/icon-16.png
rsvg-convert -w 32 -h 32 assets/app-icon.svg -o assets/icon-32.png
rsvg-convert -w 64 -h 64 assets/app-icon.svg -o assets/icon-64.png
rsvg-convert -w 128 -h 128 assets/app-icon.svg -o assets/icon-128.png
rsvg-convert -w 256 -h 256 assets/app-icon.svg -o assets/icon-256.png
rsvg-convert -w 512 -h 512 assets/app-icon.svg -o assets/icon-512.png
rsvg-convert -w 1024 -h 1024 assets/app-icon.svg -o assets/icon-1024.png

# 复制通用图标
cp assets/icon-256.png assets/icon.png

# 生成 macOS .icns 文件
mkdir -p assets/icon.iconset
cp assets/icon-16.png assets/icon.iconset/icon_16x16.png
cp assets/icon-32.png assets/icon.iconset/<EMAIL>
cp assets/icon-32.png assets/icon.iconset/icon_32x32.png
cp assets/icon-64.png assets/icon.iconset/<EMAIL>
cp assets/icon-128.png assets/icon.iconset/icon_128x128.png
cp assets/icon-256.png assets/icon.iconset/<EMAIL>
cp assets/icon-256.png assets/icon.iconset/icon_256x256.png
cp assets/icon-512.png assets/icon.iconset/<EMAIL>
cp assets/icon-512.png assets/icon.iconset/icon_512x512.png
cp assets/icon-1024.png assets/icon.iconset/<EMAIL>
iconutil -c icns assets/icon.iconset -o assets/icon.icns

# 生成 Windows .ico 文件
magick assets/icon-16.png assets/icon-32.png assets/icon-64.png assets/icon-128.png assets/icon-256.png assets/icon.ico
```

3. 重新构建应用：
```bash
npm run build
npm run dist
```
